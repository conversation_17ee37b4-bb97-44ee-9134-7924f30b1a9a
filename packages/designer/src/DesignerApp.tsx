import React from 'react';
import { DesignerProvider } from './context/DesignerContext';
import { Designer } from './components/Designer';
import { PageSchema } from '@lowcode/renderer';

// 示例页面Schema，包含设计稿中的业务组件
const exampleSchema: PageSchema = {
  id: 'example_page',
  title: '示例页面',
  components: [
    {
      id: 'header_nav',
      type: 'TopNavigation',
      props: {
        brand: '全知科技',
        items: [
          { key: 'overview', label: '概览', active: true },
          { key: 'apis', label: 'APIs' },
          { key: 'data', label: '数据' },
          { key: 'insights', label: '洞察' },
          { key: 'risks', label: '风险' },
          { key: 'audit', label: '审计' },
          { key: 'config', label: '配置' }
        ]
      },
      style: {
        marginBottom: '24px'
      }
    },
    {
      id: 'search_form',
      type: 'TableViewWithSearch',
      props: {
        searchFields: [
          {
            key: 'app',
            label: '应用',
            type: 'select',
            placeholder: '请选择',
            options: []
          },
          {
            key: 'appName',
            label: '应用名称',
            type: 'input',
            placeholder: '请输入'
          },
          {
            key: 'accessMode',
            label: '访问模式',
            type: 'select',
            placeholder: '请选择',
            options: []
          },
          {
            key: 'terminalType',
            label: '终端类型',
            type: 'select',
            placeholder: '请选择',
            options: []
          },
          {
            key: 'requestType',
            label: '请求类型',
            type: 'select',
            placeholder: '请选择',
            options: []
          },
          {
            key: 'responseAlert',
            label: '响应告警',
            type: 'select',
            placeholder: '请选择',
            options: []
          },
          {
            key: 'firstFoundTime',
            label: '首次发现时间',
            type: 'date',
            placeholder: '年 / 月 / 日'
          },
          {
            key: 'apiSensitiveLevel',
            label: 'API敏感等级',
            type: 'select',
            placeholder: '请选择',
            options: []
          }
        ],
        columns: [
          {
            key: 'path',
            title: '路径',
            dataIndex: 'path',
            width: 200
          },
          {
            key: 'sensitiveLevel',
            title: 'API敏感等级',
            dataIndex: 'sensitiveLevel',
            width: 120
          },
          {
            key: 'accessCount',
            title: '累计访问次数',
            dataIndex: 'accessCount',
            width: 120
          },
          {
            key: 'coverage',
            title: '目检覆盖率',
            dataIndex: 'coverage',
            width: 120
          },
          {
            key: 'ip',
            title: 'IP',
            dataIndex: 'ip',
            width: 120
          },
          {
            key: 'firstFoundTime',
            title: '首次发现时间',
            dataIndex: 'firstFoundTime',
            width: 160
          },
          {
            key: 'actions',
            title: '操作',
            dataIndex: 'actions',
            width: 80
          }
        ],
        dataSource: [
          {
            id: 1,
            path: '/k8s-api/${param1}/${param2}',
            sensitiveLevel: '高敏感',
            accessCount: '6.4k',
            coverage: '—',
            ip: '*************',
            firstFoundTime: '2025-08-14 19:44:25'
          },
          {
            id: 2,
            path: '/login',
            sensitiveLevel: '高敏感',
            accessCount: '3.6k',
            coverage: '—',
            ip: '***********',
            firstFoundTime: '2025-08-14 19:38:21'
          },
          {
            id: 3,
            path: '/abnormal',
            sensitiveLevel: '高敏感',
            accessCount: '2.5k',
            coverage: '—',
            ip: '***********1',
            firstFoundTime: '2025-08-14 19:02:19'
          },
          {
            id: 4,
            path: '/login',
            sensitiveLevel: '高敏感',
            accessCount: '502',
            coverage: '—',
            ip: '************',
            firstFoundTime: '2025-08-14 19:00:18'
          },
          {
            id: 5,
            path: '/login',
            sensitiveLevel: '高敏感',
            accessCount: '327',
            coverage: '—',
            ip: '************',
            firstFoundTime: '2025-08-14 19:00:17'
          }
        ]
      }
    }
  ],
  apis: [],
  theme: {},
  layout: {
    type: 'admin',
    header: true,
    sidebar: false,
    footer: false
  }
};

export interface DesignerAppProps {
  initialSchema?: PageSchema;
}

export const DesignerApp: React.FC<DesignerAppProps> = ({ 
  initialSchema = exampleSchema 
}) => {
  return (
    <DesignerProvider initialSchema={initialSchema}>
      <Designer />
    </DesignerProvider>
  );
};

export default DesignerApp;
