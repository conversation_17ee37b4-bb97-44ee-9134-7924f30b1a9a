import React from 'react';
import { useDesigner } from '../../context/DesignerContext';

export interface ToolbarProps {
  height?: number;
  style?: React.CSSProperties;
  className?: string;
}

export const Toolbar: React.FC<ToolbarProps> = ({ height = 48, style, className }) => {
  const { canvasState, setCanvasMode } = useDesigner();

  const defaultStyle: React.CSSProperties = {
    height,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '0 24px',
    backgroundColor: '#ffffff',
    borderBottom: '1px solid #e8e8e8',
    flexShrink: 0,
    zIndex: 20,
    ...style
  };

  const togglePreview = () => {
    setCanvasMode(canvasState.mode === 'design' ? 'preview' : 'design');
  };



  return (
    <header className={`lowcode-toolbar ${className || ''}`} style={defaultStyle}>
      {/* 左侧标题 */}
      <h1 style={{
        margin: 0,
        fontSize: '20px',
        fontWeight: '700',
        color: '#1f2937'
      }}>
        低代码平台
      </h1>

      {/* 右侧操作按钮 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '16px'
      }}>
        <button
          onClick={togglePreview}
          style={{
            backgroundColor: '#2563eb',
            color: '#ffffff',
            fontWeight: '600',
            padding: '8px 16px',
            borderRadius: '6px',
            border: 'none',
            fontSize: '14px',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#1d4ed8';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#2563eb';
          }}
        >
          {canvasState.mode === 'design' ? '预览' : '设计'}
        </button>
      </div>
    </header>
  );
};

