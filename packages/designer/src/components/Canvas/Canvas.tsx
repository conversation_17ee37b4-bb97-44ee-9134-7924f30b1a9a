import React, { useRef, useState } from 'react';
import { createRenderer, findComponent } from '@lowcode/renderer';
import { useDesigner } from '../../context/DesignerContext';

export interface CanvasProps {
  style?: React.CSSProperties;
  className?: string;
  onComponentSelect?: () => void;
  onCanvasClick?: () => void;
}

const CanvasComponent: React.FC<CanvasProps> = ({
  style,
  className,
  onComponentSelect,
  onCanvasClick
}) => {
  const { schema, canvasState, selectComponent, hoverComponent } = useDesigner();
  const canvasRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef(createRenderer());
  const [contextMenu, setContextMenu] = useState<{
    show: boolean;
    x: number;
    y: number;
    componentId?: string;
  }>({ show: false, x: 0, y: 0 });

  // 处理组件点击
  const handleComponentClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    const target = event.target as HTMLElement;
    const componentElement = target.closest('[data-component-id]');

    if (componentElement) {
      const componentId = componentElement.getAttribute('data-component-id');
      selectComponent(componentId || undefined);
      onComponentSelect?.();
    } else {
      selectComponent(undefined);
      onCanvasClick?.();
    }
  };

  // 处理右键菜单
  const handleContextMenu = (event: React.MouseEvent) => {
    event.preventDefault();
    const target = event.target as HTMLElement;
    const componentElement = target.closest('[data-component-id]');

    if (componentElement) {
      const componentId = componentElement.getAttribute('data-component-id');
      setContextMenu({
        show: true,
        x: event.clientX,
        y: event.clientY,
        componentId: componentId || undefined
      });
    }
  };

  // 处理组件悬停
  const handleComponentMouseEnter = (event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    const componentElement = target.closest('[data-component-id]');

    if (componentElement) {
      const componentId = componentElement.getAttribute('data-component-id');
      hoverComponent(componentId || undefined);
    }
  };

  // 处理组件离开悬停
  const handleComponentMouseLeave = () => {
    hoverComponent(undefined);
  };

  // 渲染页面内容
  const renderPageContent = () => {
    if (canvasState.mode === 'preview') {
      // 预览模式：直接渲染
      return rendererRef.current.renderPage(schema);
    } else {
      // 设计模式：添加设计时交互
      const pageElement = rendererRef.current.renderPage(schema);

      return React.cloneElement(pageElement, {
        onClick: handleComponentClick,
        onMouseEnter: handleComponentMouseEnter,
        onMouseLeave: handleComponentMouseLeave,
        style: {
          ...pageElement.props.style,
          cursor: 'pointer',
          position: 'relative'
        }
      });
    }
  };

  // 渲染选中状态指示器
  const renderSelectionIndicator = () => {
    if (!canvasState.selectedComponentId || canvasState.mode === 'preview') {
      return null;
    }

    return (
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          border: '2px solid #1890ff',
          borderRadius: '4px',
          pointerEvents: 'none',
          zIndex: 1000
        }}
      />
    );
  };

  // 渲染悬停状态指示器
  const renderHoverIndicator = () => {
    if (!canvasState.hoveredComponentId ||
        canvasState.hoveredComponentId === canvasState.selectedComponentId ||
        canvasState.mode === 'preview') {
      return null;
    }

    return (
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          border: '2px dashed #52c41a',
          borderRadius: '4px',
          pointerEvents: 'none',
          zIndex: 999
        }}
      />
    );
  };

  // 获取面包屑路径
  const getBreadcrumbPath = () => {
    const path = ['页面'];
    if (canvasState.selectedComponentId) {
      const component = findComponent(schema.components, canvasState.selectedComponentId);
      if (component) {
        // 简化的面包屑，实际应该递归查找父组件
        if (component.type === 'TableViewWithSearch') {
          path.push('表格搜索视图');
        } else if (component.type === 'TopNavigation') {
          path.push('导航栏');
        } else {
          path.push(component.type);
        }
      }
    }
    return path;
  };

  const defaultStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: '#f0f2f5',
    overflow: 'hidden',
    position: 'relative',
    ...style,
  };

  return (
    <main
      ref={canvasRef}
      className={`lowcode-canvas ${className || ''}`}
      style={defaultStyle}
      onClick={handleComponentClick}
      onContextMenu={handleContextMenu}
    >
      {/* 画布内容区域 */}
      <div style={{
        flex: 1,
        overflow: 'auto',
        padding: '24px',
        backgroundColor: '#f0f2f5',
        position: 'relative'
      }}>
        {/* 页面容器 */}
        <div
          style={{
            backgroundColor: '#ffffff',
            padding: '24px',
            borderRadius: '8px',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
            border: '1px solid #e5e7eb',
            minHeight: '600px',
            position: 'relative'
          }}
          data-component-id="pageContainer"
        >
          {/* 页面内容 */}
          {renderPageContent()}
          {renderSelectionIndicator()}
          {renderHoverIndicator()}

          {/* 空状态提示 */}
          {schema.components.length === 0 && (
            <div
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                textAlign: 'center',
                color: '#9ca3af',
                fontSize: '16px'
              }}
            >
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>📦</div>
              <div>拖拽组件到这里开始设计</div>
            </div>
          )}
        </div>
      </div>

      {/* 底部面包屑导航 */}
      {canvasState.selectedComponentId && (
        <div style={{
          position: 'absolute',
          bottom: '16px',
          left: '50%',
          transform: 'translateX(-50%)',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(8px)',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          border: '1px solid #e5e7eb',
          padding: '8px 12px',
          fontSize: '14px',
          zIndex: 10,
          transition: 'all 0.2s ease'
        }}>
          <nav style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            color: '#6b7280'
          }}>
            {getBreadcrumbPath().map((item, index, array) => (
              <React.Fragment key={index}>
                <button
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: index === array.length - 1 ? '#dbeafe' : 'transparent',
                    color: index === array.length - 1 ? '#1d4ed8' : '#6b7280',
                    fontWeight: index === array.length - 1 ? '600' : '400',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    border: 'none',
                    cursor: 'move',
                    fontSize: '14px',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (index !== array.length - 1) {
                      e.currentTarget.style.backgroundColor = '#f3f4f6';
                      e.currentTarget.style.color = '#374151';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (index !== array.length - 1) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                      e.currentTarget.style.color = '#6b7280';
                    }
                  }}
                >
                  <span style={{ marginRight: '4px' }}>⋮⋮</span>
                  {item}
                </button>
                {index < array.length - 1 && (
                  <span style={{ fontSize: '16px' }}>▶</span>
                )}
              </React.Fragment>
            ))}
          </nav>
        </div>
      )}

      {/* 右键菜单 */}
      {contextMenu.show && (
        <div
          style={{
            position: 'fixed',
            left: `${contextMenu.x}px`,
            top: `${contextMenu.y}px`,
            backgroundColor: '#ffffff',
            border: '1px solid #e5e7eb',
            borderRadius: '6px',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',
            padding: '4px 0',
            minWidth: '192px',
            zIndex: 50,
            fontSize: '14px'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <button
            style={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              padding: '8px 16px',
              border: 'none',
              backgroundColor: 'transparent',
              color: '#374151',
              cursor: 'pointer',
              fontSize: '14px'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f3f4f6';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <span style={{ marginRight: '8px' }}>📋</span> 复制组件
          </button>
          <button
            style={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              padding: '8px 16px',
              border: 'none',
              backgroundColor: 'transparent',
              color: '#374151',
              cursor: 'pointer',
              fontSize: '14px'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f3f4f6';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <span style={{ marginRight: '8px' }}>📄</span> 粘贴到内部
          </button>
          <hr style={{ margin: '4px 0', border: 'none', borderTop: '1px solid #e5e7eb' }} />
          <button
            style={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              padding: '8px 16px',
              border: 'none',
              backgroundColor: 'transparent',
              color: '#dc2626',
              cursor: 'pointer',
              fontSize: '14px'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#fef2f2';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <span style={{ marginRight: '8px' }}>🗑️</span> 删除组件
          </button>
        </div>
      )}

      {/* 点击其他地方关闭右键菜单 */}
      {contextMenu.show && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 40
          }}
          onClick={() => setContextMenu({ show: false, x: 0, y: 0 })}
        />
      )}
    </main>
  );
};

export const Canvas = React.memo(CanvasComponent);
