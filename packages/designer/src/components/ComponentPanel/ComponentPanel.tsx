import React, { useState } from 'react';
import { ComponentMeta, createDefaultComponentSchema } from '@lowcode/renderer';
import { useDesigner } from '../../context/DesignerContext';
import { ComponentPanelItem } from '../../types';

export interface ComponentPanelProps {
  width?: number;
  style?: React.CSSProperties;
  className?: string;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

// 模拟组件库数据
const mockComponentLibrary: ComponentPanelItem[] = [
  {
    category: 'basic',
    components: [
      {
        type: 'Text',
        name: '文本',
        description: '基础文本组件',
        category: 'basic',
        icon: '📝',
        props: [],
        defaultProps: { children: '文本内容' }
      },
      {
        type: 'Button',
        name: '按钮',
        description: '基础按钮组件',
        category: 'basic',
        icon: '🔘',
        props: [],
        defaultProps: { children: '按钮' }
      },
      {
        type: 'Input',
        name: '输入框',
        description: '基础输入框组件',
        category: 'basic',
        icon: '📝',
        props: [],
        defaultProps: { placeholder: '请输入内容' }
      },
      {
        type: 'Container',
        name: '容器',
        description: '基础容器组件',
        category: 'basic',
        icon: '📦',
        props: [],
        defaultProps: {}
      }
    ]
  },
  {
    category: 'business',
    components: [
      {
        type: 'TopNavigation',
        name: '顶部导航',
        description: '顶部导航组件',
        category: 'business',
        icon: '🧭',
        props: [],
        defaultProps: {}
      },
      {
        type: 'SidebarTreeView',
        name: '侧边栏树形导航',
        description: '侧边栏树形导航组件',
        category: 'business',
        icon: '🌳',
        props: [],
        defaultProps: {}
      },
      {
        type: 'TableViewWithSearch',
        name: '表格搜索视图',
        description: '集成搜索、工具栏、表格功能的复合组件',
        category: 'business',
        icon: '📊',
        props: [],
        defaultProps: {}
      },
      {
        type: 'StatusBar',
        name: '状态栏',
        description: '底部状态栏组件',
        category: 'business',
        icon: '📊',
        props: [],
        defaultProps: {}
      }
    ]
  },
  {
    category: 'layout',
    components: [
      {
        type: 'AdminLayout',
        name: '管理布局',
        description: '管理后台布局组件',
        category: 'layout',
        icon: '🏗️',
        props: [],
        defaultProps: {}
      }
    ]
  }
];



export const ComponentPanel: React.FC<ComponentPanelProps> = ({
  width = 264,
  style,
  className,
  isCollapsed = false,
  onToggleCollapse
}) => {
  const { addComponent } = useDesigner();
  const [activeCategory, setActiveCategory] = useState<string>('basics');
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({
    basics: true,
    business: false,
    layout: false
  });

  // 处理组件拖拽开始
  const handleDragStart = (event: React.DragEvent, meta: ComponentMeta) => {
    event.dataTransfer.setData('application/json', JSON.stringify(meta));
    event.dataTransfer.effectAllowed = 'copy';
  };

  // 处理组件点击添加
  const handleComponentClick = (meta: ComponentMeta) => {
    const componentSchema = createDefaultComponentSchema(meta.type, meta);
    addComponent(componentSchema);
  };

  // 切换分类展开状态
  const toggleCategory = (category: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  const defaultStyle: React.CSSProperties = {
    width: `${width}px`,
    height: '100%',
    backgroundColor: '#ffffff',
    borderRight: '1px solid #e8e8e8',
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    position: 'relative',
    ...style,
  };

  return (
    <div className={`lowcode-component-panel ${className || ''}`} style={defaultStyle}>
      {/* 折叠/展开按钮 */}
      <button
        onClick={onToggleCollapse}
        style={{
          position: 'absolute',
          right: '-12px',
          top: '50%',
          transform: 'translateY(-50%)',
          zIndex: 10,
          width: '24px',
          height: '24px',
          backgroundColor: '#ffffff',
          border: '1px solid #d9d9d9',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          fontSize: '14px',
          color: '#666666',
          transition: 'all 0.3s ease',
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = '#f5f5f5';
          e.currentTarget.style.borderColor = '#1890ff';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = '#ffffff';
          e.currentTarget.style.borderColor = '#d9d9d9';
        }}
      >
        {isCollapsed ? '▶' : '◀'}
      </button>

      {/* 面板内容 */}
      <div style={{
        opacity: isCollapsed ? 0 : 1,
        transition: 'opacity 0.3s ease',
        height: '100%',
        display: 'flex',
        overflow: 'hidden'
      }}>
        {/* 左侧分类导航 */}
        <div style={{
          width: '80px',
          borderRight: '1px solid #e8e8e8',
          flexShrink: 0,
          backgroundColor: '#ffffff'
        }}>
          <nav style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '4px',
            padding: '8px'
          }}>
            {[
              { key: 'layout', icon: '⚏', label: '布局' },
              { key: 'basics', icon: '🧩', label: '基础' },
              { key: 'business', icon: '💼', label: '业务' }
            ].map(category => (
              <button
                key={category.key}
                onClick={() => setActiveCategory(category.key)}
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  padding: '8px',
                  border: 'none',
                  borderLeft: `4px solid ${activeCategory === category.key ? '#1890ff' : 'transparent'}`,
                  borderRadius: '6px',
                  backgroundColor: activeCategory === category.key ? '#e6f7ff' : 'transparent',
                  color: activeCategory === category.key ? '#1890ff' : '#666666',
                  cursor: 'pointer',
                  fontSize: '12px',
                  fontWeight: activeCategory === category.key ? '600' : '400',
                  transition: 'all 0.2s ease',
                  minHeight: '60px',
                  justifyContent: 'center'
                }}
                onMouseEnter={(e) => {
                  if (activeCategory !== category.key) {
                    e.currentTarget.style.backgroundColor = '#f5f5f5';
                    e.currentTarget.style.color = '#333333';
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeCategory !== category.key) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#666666';
                  }
                }}
              >
                <span style={{ fontSize: '20px', marginBottom: '4px' }}>
                  {category.icon}
                </span>
                <span style={{ fontSize: '12px', fontWeight: '500' }}>
                  {category.label}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* 右侧组件列表 */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          padding: '12px',
          backgroundColor: '#ffffff'
        }}>
          {mockComponentLibrary
            .filter(category => category.category === activeCategory)
            .map(category => (
              <div key={category.category}>
                {/* 基础组件分组 */}
                {category.category === 'basics' && (
                  <div style={{ marginBottom: '16px' }}>
                    <div
                      onClick={() => toggleCategory('basics')}
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        padding: '4px 0',
                        cursor: 'pointer',
                        fontSize: '12px',
                        fontWeight: '600',
                        color: '#333333'
                      }}
                    >
                      <span>基础组件</span>
                      <span style={{
                        transform: expandedCategories.basics ? 'rotate(180deg)' : 'rotate(0deg)',
                        transition: 'transform 0.2s ease',
                        fontSize: '14px'
                      }}>
                        ▼
                      </span>
                    </div>

                    {expandedCategories.basics && (
                      <div style={{
                        display: 'grid',
                        gridTemplateColumns: '1fr',
                        gap: '8px',
                        marginTop: '8px'
                      }}>
                        {category.components.map(component => (
                          <div
                            key={component.type}
                            draggable
                            onDragStart={(e) => handleDragStart(e, component)}
                            onClick={() => handleComponentClick(component)}
                            style={{
                              padding: '8px',
                              backgroundColor: '#ffffff',
                              border: '1px solid #e8e8e8',
                              borderRadius: '6px',
                              cursor: 'move',
                              textAlign: 'center',
                              transition: 'all 0.2s ease',
                              userSelect: 'none'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.borderColor = '#1890ff';
                              e.currentTarget.style.backgroundColor = '#f0f9ff';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.borderColor = '#e8e8e8';
                              e.currentTarget.style.backgroundColor = '#ffffff';
                            }}
                          >
                            <div style={{ fontSize: '24px', marginBottom: '4px' }}>
                              {component.icon}
                            </div>
                            <div style={{
                              fontSize: '12px',
                              fontWeight: '500',
                              color: '#333333'
                            }}>
                              {component.name}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* 其他分类的组件 */}
                {category.category !== 'basics' && (
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: '1fr',
                    gap: '8px'
                  }}>
                    {category.components.map(component => (
                      <div
                        key={component.type}
                        draggable
                        onDragStart={(e) => handleDragStart(e, component)}
                        onClick={() => handleComponentClick(component)}
                        style={{
                          padding: '8px',
                          backgroundColor: '#ffffff',
                          border: '1px solid #e8e8e8',
                          borderRadius: '6px',
                          cursor: 'move',
                          textAlign: 'center',
                          transition: 'all 0.2s ease',
                          userSelect: 'none'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.borderColor = '#1890ff';
                          e.currentTarget.style.backgroundColor = '#f0f9ff';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.borderColor = '#e8e8e8';
                          e.currentTarget.style.backgroundColor = '#ffffff';
                        }}
                      >
                        <div style={{ fontSize: '24px', marginBottom: '4px' }}>
                          {component.icon}
                        </div>
                        <div style={{
                          fontSize: '12px',
                          fontWeight: '500',
                          color: '#333333'
                        }}>
                          {component.name}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};
