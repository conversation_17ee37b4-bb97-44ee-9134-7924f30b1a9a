import React, { useMemo, useState } from 'react';
import { ApiConfig } from '@lowcode/renderer';
import { useDesigner } from '../../context/DesignerContext';

export interface ApiPanelProps {
  width?: number;
  style?: React.CSSProperties;
  className?: string;
}

const JsonEditor: React.FC<{ value?: any; onChange: (v: any) => void; placeholder?: string }> = ({ value, onChange, placeholder }) => {
  const [text, setText] = useState(() => (value ? JSON.stringify(value, null, 2) : ''));
  const [error, setError] = useState<string | null>(null);
  return (
    <div>
      <textarea
        style={{ width: '100%', minHeight: 100, fontFamily: 'monospace' }}
        value={text}
        onChange={(e) => {
          const t = e.target.value;
          setText(t);
          if (t.trim() === '') {
            setError(null);
            onChange(undefined);
            return;
          }
          try {
            const json = JSON.parse(t);
            setError(null);
            onChange(json);
          } catch (err) {
            setError('JSON 格式错误');
          }
        }}
        placeholder={placeholder}
      />
      {error && <div style={{ color: '#ff4d4f', fontSize: 12, marginTop: 4 }}>{error}</div>}
    </div>
  );
};

export const ApiPanel: React.FC<ApiPanelProps> = ({ width = 360, style, className }) => {
  const { schema, updateSchema } = useDesigner();

  const apis = useMemo(() => schema.apis || [], [schema.apis]);

  const defaultStyle: React.CSSProperties = {
    width,
    height: '100%',
    background: '#fafafa',
    borderLeft: '1px solid #e8e8e8',
    display: 'flex',
    flexDirection: 'column',
    ...style
  };

  const addApi = () => {
    const id = `api_${Date.now().toString(36)}`;
    const next: ApiConfig = { id, name: `未命名API`, url: '/api/path', method: 'GET' };
    updateSchema({ ...schema, apis: [...apis, next] });
  };

  const removeApi = (id: string) => {
    updateSchema({ ...schema, apis: apis.filter((a: ApiConfig) => a.id !== id) });
  };

  const updateApi = (id: string, patch: Partial<ApiConfig>) => {
    const next = apis.map((a: ApiConfig) => (a.id === id ? { ...a, ...patch } : a));
    updateSchema({ ...schema, apis: next });
  };

  const testApi = async (api: ApiConfig) => {
    // 占位：仅打印到控制台，避免引入新依赖
    console.log('Test API:', api);
    alert(`已在控制台打印测试调用: ${api.method} ${api.url}`);
  };

  return (
    <div className={`lowcode-api-panel ${className || ''}`} style={defaultStyle}>
      <div style={{ padding: 16, borderBottom: '1px solid #e8e8e8', background: '#fff', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div style={{ fontSize: 16, fontWeight: 600 }}>API 配置</div>
        <button onClick={addApi} style={{ padding: '6px 12px' }}>新增 API</button>
      </div>

      <div style={{ flex: 1, overflow: 'auto', padding: 16 }}>
        {apis.length === 0 && (
          <div style={{ color: '#999' }}>暂无 API，点击右上角“新增 API”创建</div>
        )}

        {apis.map((api: ApiConfig) => (
          <div key={api.id} style={{ background: '#fff', border: '1px solid #e8e8e8', borderRadius: 8, marginBottom: 16, overflow: 'hidden' }}>
            <div style={{ padding: 12, borderBottom: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ fontWeight: 600 }}>{api.name || api.id}</div>
              <div style={{ display: 'flex', gap: 8 }}>
                <button onClick={() => testApi(api)}>测试</button>
                <button onClick={() => removeApi(api.id)} style={{ color: '#ff4d4f' }}>删除</button>
              </div>
            </div>

            <div style={{ padding: 12, display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 12 }}>
              <div>
                <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>名称</div>
                <input style={{ width: '100%' }} value={api.name || ''} onChange={(e) => updateApi(api.id, { name: e.target.value })} />
              </div>
              <div>
                <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>方法</div>
                <select value={api.method} onChange={(e) => updateApi(api.id, { method: e.target.value as ApiConfig['method'] })} style={{ width: '100%' }}>
                  <option value="GET">GET</option>
                  <option value="POST">POST</option>
                  <option value="PUT">PUT</option>
                  <option value="DELETE">DELETE</option>
                </select>
              </div>
              <div style={{ gridColumn: '1 / span 2' }}>
                <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>URL</div>
                <input style={{ width: '100%' }} value={api.url} onChange={(e) => updateApi(api.id, { url: e.target.value })} />
              </div>

              <div style={{ gridColumn: '1 / span 2' }}>
                <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>Headers (JSON)</div>
                <JsonEditor value={api.headers} onChange={(v) => updateApi(api.id, { headers: v })} placeholder={`{\n  \"Authorization\": \"Bearer xxx\"\n}` } />
              </div>
              <div style={{ gridColumn: '1 / span 2' }}>
                <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>Params/Body (JSON)</div>
                <JsonEditor value={api.params} onChange={(v) => updateApi(api.id, { params: v })} placeholder={`{\n  \"page\": 1,\n  \"size\": 20\n}` } />
              </div>
              <div style={{ gridColumn: '1 / span 2' }}>
                <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>数据路径 dataPath</div>
                <input style={{ width: '100%' }} value={api.dataPath || ''} onChange={(e) => updateApi(api.id, { dataPath: e.target.value })} placeholder="例如 data.list 或 result.items" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

