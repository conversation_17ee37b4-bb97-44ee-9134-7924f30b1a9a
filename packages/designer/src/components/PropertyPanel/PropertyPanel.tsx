import React, { useMemo, useRef, useState } from 'react';
import { create<PERSON><PERSON><PERSON>, ComponentMeta, PropMeta, ComponentSchema, findComponent } from '@lowcode/renderer';
import { useDesigner } from '../../context/DesignerContext';

export interface PropertyPanelProps {
  width?: number;
  style?: React.CSSProperties;
  className?: string;
  onClose?: () => void;
}

const Field: React.FC<{
  meta: PropMeta;
  value: any;
  onChange: (v: any) => void;
}> = ({ meta, value, onChange }) => {
  const commonStyle: React.CSSProperties = {
    width: '100%',
    boxSizing: 'border-box',
    padding: '8px 12px',
    border: '1px solid #d1d5db',
    borderRadius: '6px',
    fontSize: '14px',
    outline: 'none',
    transition: 'border-color 0.2s ease'
  };

  const handleFocus = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    e.currentTarget.style.borderColor = '#2563eb';
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    e.currentTarget.style.borderColor = '#d1d5db';
  };

  switch (meta.type) {
    case 'string':
      return (
        <input
          style={commonStyle}
          type="text"
          value={value ?? ''}
          onChange={(e) => onChange(e.target.value)}
          onFocus={handleFocus}
          onBlur={handleBlur}
        />
      );
    case 'number':
      return (
        <input
          style={commonStyle}
          type="number"
          value={value ?? ''}
          onChange={(e) => {
            const v = e.target.value;
            onChange(v === '' ? undefined : Number(v));
          }}
          onFocus={handleFocus}
          onBlur={handleBlur}
        />
      );
    case 'boolean':
      return (
        <div style={{
          display: 'flex',
          gap: '16px',
          marginTop: '8px'
        }}>
          <label style={{
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer'
          }}>
            <input
              type="radio"
              name={`${meta.name}-radio`}
              checked={!!value}
              onChange={() => onChange(true)}
              style={{
                width: '16px',
                height: '16px',
                accentColor: '#2563eb',
                marginRight: '8px'
              }}
            />
            <span style={{
              fontSize: '14px',
              color: '#374151'
            }}>
              是
            </span>
          </label>
          <label style={{
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer'
          }}>
            <input
              type="radio"
              name={`${meta.name}-radio`}
              checked={!value}
              onChange={() => onChange(false)}
              style={{
                width: '16px',
                height: '16px',
                accentColor: '#2563eb',
                marginRight: '8px'
              }}
            />
            <span style={{
              fontSize: '14px',
              color: '#374151'
            }}>
              否
            </span>
          </label>
        </div>
      );
    case 'object':
    case 'array': {
      const [text, setText] = useState(() =>
        value !== undefined ? JSON.stringify(value, null, 2) : ''
      );
      const [error, setError] = useState<string | null>(null);
      return (
        <div>
          <textarea
            style={{
              ...commonStyle,
              fontFamily: 'Monaco, Consolas, "Courier New", monospace',
              minHeight: '120px',
              backgroundColor: '#f9fafb',
              resize: 'vertical'
            }}
            value={text}
            onChange={(e) => {
              const t = e.target.value;
              setText(t);
              if (t.trim() === '') {
                setError(null);
                onChange(undefined);
                return;
              }
              try {
                const json = JSON.parse(t);
                setError(null);
                onChange(json);
              } catch (err) {
                setError('JSON 格式错误');
              }
            }}
            placeholder={`请输入${meta.type === 'object' ? '对象' : '数组'}（JSON）`}
            onFocus={handleFocus}
            onBlur={handleBlur}
          />
          {error && (
            <div style={{
              color: '#dc2626',
              fontSize: '12px',
              marginTop: '4px'
            }}>
              {error}
            </div>
          )}
        </div>
      );
    }
    case 'function':
    default:
      return (
        <input
          style={commonStyle}
          type="text"
          value={value ?? ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={`不支持的类型(${meta.type})，以字符串方式编辑`}
          onFocus={handleFocus}
          onBlur={handleBlur}
        />
      );
  }
};

export const PropertyPanel: React.FC<PropertyPanelProps> = ({
  width = 384,
  style,
  className,
  onClose
}) => {
  const { schema, canvasState, updateComponent } = useDesigner();
  const rendererRef = useRef(createRenderer());

  const selectedComponent: ComponentSchema | null = useMemo(() => {
    if (!canvasState.selectedComponentId) return null;
    return findComponent(schema.components, canvasState.selectedComponentId);
  }, [schema.components, canvasState.selectedComponentId]);

  const meta: ComponentMeta | undefined = useMemo(() => {
    if (!selectedComponent) return undefined;
    return rendererRef.current.getComponentRegistry().getMeta(selectedComponent.type);
  }, [selectedComponent]);

  const defaultStyle: React.CSSProperties = {
    width,
    height: '100%',
    backgroundColor: '#ffffff',
    borderLeft: '1px solid #e5e7eb',
    display: 'flex',
    flexDirection: 'column',
    boxShadow: '-4px 0 12px rgba(0, 0, 0, 0.1)',
    ...style
  };

  // 获取组件显示名称
  const getComponentDisplayName = (component: ComponentSchema | null) => {
    if (!component) return '';

    const typeMap: Record<string, string> = {
      'TableViewWithSearch': '表格搜索视图',
      'TopNavigation': '导航栏',
      'Text': '文本',
      'Button': '按钮',
      'Input': '输入框',
      'Container': '容器'
    };

    return typeMap[component.type] || component.type;
  };

  const handlePropChange = (name: string, value: any) => {
    if (!selectedComponent) return;
    const nextProps = { ...(selectedComponent.props || {}) } as Record<string, any>;
    if (value === undefined) {
      delete nextProps[name];
    } else {
      nextProps[name] = value;
    }
    updateComponent(selectedComponent.id, { props: nextProps });
  };

  const getInitialValue = (p: PropMeta) => {
    const current = selectedComponent?.props?.[p.name];
    if (current !== undefined) return current;
    return p.default;
  };

  if (!selectedComponent) {
    return (
      <aside className={`lowcode-property-panel ${className || ''}`} style={defaultStyle}>
        {/* 标题栏 */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '24px',
          borderBottom: '1px solid #e5e7eb',
          flexShrink: 0
        }}>
          <div>
            <h2 style={{
              margin: 0,
              fontSize: '18px',
              fontWeight: '600',
              color: '#1f2937'
            }}>
              已选组件
            </h2>
            <p style={{
              margin: '4px 0 0 0',
              fontSize: '14px',
              color: '#6b7280'
            }}>
              请选择画布中的组件
            </p>
          </div>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '20px',
              color: '#6b7280',
              cursor: 'pointer',
              padding: '4px',
              borderRadius: '4px',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f3f4f6';
              e.currentTarget.style.color = '#374151';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.color = '#6b7280';
            }}
          >
            ✕
          </button>
        </div>

        <div style={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#9ca3af',
          fontSize: '16px'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>🎯</div>
            <div>选择组件查看属性</div>
          </div>
        </div>
      </aside>
    );
  }

  return (
    <aside className={`lowcode-property-panel ${className || ''}`} style={defaultStyle}>
      {/* 标题栏 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '24px',
        borderBottom: '1px solid #e5e7eb',
        flexShrink: 0
      }}>
        <div>
          <h2 style={{
            margin: 0,
            fontSize: '18px',
            fontWeight: '600',
            color: '#1f2937'
          }}>
            已选组件
          </h2>
          <p style={{
            margin: '4px 0 0 0',
            fontSize: '14px',
            color: '#6b7280'
          }}>
            {getComponentDisplayName(selectedComponent)}
          </p>
        </div>
        <button
          onClick={onClose}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '20px',
            color: '#6b7280',
            cursor: 'pointer',
            padding: '4px',
            borderRadius: '4px',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#f3f4f6';
            e.currentTarget.style.color = '#374151';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.color = '#6b7280';
          }}
        >
          ✕
        </button>
      </div>

      {/* 属性编辑区 */}
      <div style={{
        flex: 1,
        overflow: 'auto',
        padding: '0 24px 24px 24px'
      }}>
        {/* 基本信息 */}
        <div style={{ marginBottom: '24px' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            marginBottom: '16px'
          }}>
            <label style={{
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151'
            }}>
              名称
            </label>
            <span style={{
              color: '#dc2626',
              marginLeft: '4px',
              fontSize: '14px'
            }}>
              *
            </span>
          </div>
          <input
            type="text"
            value={selectedComponent.id || ''}
            onChange={(e) => updateComponent(selectedComponent.id, { id: e.target.value })}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              fontSize: '14px',
              outline: 'none',
              transition: 'border-color 0.2s ease'
            }}
            onFocus={(e) => {
              e.currentTarget.style.borderColor = '#2563eb';
            }}
            onBlur={(e) => {
              e.currentTarget.style.borderColor = '#d1d5db';
            }}
          />
        </div>

        {/* 动态属性 */}
        {(meta?.props || []).map((p) => (
          <div key={p.name} style={{ marginBottom: '20px' }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '8px'
            }}>
              <label style={{
                fontSize: '14px',
                fontWeight: '500',
                color: '#374151'
              }}>
                {p.name}
              </label>
              {p.required && (
                <span style={{
                  color: '#dc2626',
                  marginLeft: '4px',
                  fontSize: '14px'
                }}>
                  *
                </span>
              )}
            </div>
            {p.description && (
              <div style={{
                color: '#6b7280',
                fontSize: '12px',
                marginBottom: '8px'
              }}>
                {p.description}
              </div>
            )}
            <Field meta={p} value={getInitialValue(p)} onChange={(v) => handlePropChange(p.name, v)} />
          </div>
        ))}

        {/* 通用属性 */}
        <div style={{
          marginTop: '24px',
          paddingTop: '20px',
          borderTop: '1px solid #e5e7eb'
        }}>
          <div style={{
            fontSize: '16px',
            fontWeight: '600',
            color: '#374151',
            marginBottom: '16px'
          }}>
            通用属性
          </div>

          <div style={{ marginBottom: '20px' }}>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151',
              marginBottom: '8px'
            }}>
              className
            </label>
            <input
              type="text"
              value={selectedComponent.className || ''}
              onChange={(e) => updateComponent(selectedComponent.id, { className: e.target.value })}
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px',
                outline: 'none',
                transition: 'border-color 0.2s ease'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = '#2563eb';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db';
              }}
            />
          </div>

          <div>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151',
              marginBottom: '8px'
            }}>
              style (JSON)
            </label>
            <textarea
              value={JSON.stringify(selectedComponent.style || {}, null, 2)}
              onChange={(e) => {
                const val = e.target.value;
                try {
                  const obj = val ? JSON.parse(val) : {};
                  updateComponent(selectedComponent.id, { style: obj });
                } catch (err) {
                  // ignore parse error while typing
                }
              }}
              style={{
                width: '100%',
                minHeight: '120px',
                padding: '8px 12px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '12px',
                fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                backgroundColor: '#f9fafb',
                outline: 'none',
                resize: 'vertical',
                transition: 'border-color 0.2s ease'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = '#2563eb';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db';
              }}
            />
          </div>
        </div>
      </div>
    </aside>
  );
};

