import React from 'react';
import { useDesigner } from '../../context/DesignerContext';
import { PropertyPanel } from './PropertyPanel';

export interface PropertyDrawerProps {
  width?: number;
  maskClosable?: boolean;
  zIndex?: number;
}

/**
 * 右侧属性编辑抽屉：覆盖在预览区上方展示
 * 打开规则：当选中组件时（selectedComponentId 存在）即打开
 */
export const PropertyDrawer: React.FC<PropertyDrawerProps> = ({
  width = 384,
  maskClosable = true,
  zIndex = 1000
}) => {
  const { canvasState, selectComponent } = useDesigner();
  const open = !!canvasState.selectedComponentId && canvasState.mode !== 'preview';

  const drawerStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    right: 0,
    height: '100%',
    width,
    backgroundColor: '#fff',
    boxShadow: '0 0 0 1px rgba(0,0,0,0.06), 0 10px 24px rgba(0,0,0,0.12)',
    transform: `translateX(${open ? 0 : 100}%)`,
    transition: 'transform 0.3s ease-in-out',
    zIndex,
    display: 'flex',
    flexDirection: 'column',
  };

  const maskStyle: React.CSSProperties = {
    position: 'fixed',
    inset: 0 as any,
    backgroundColor: 'rgba(0,0,0,0.3)',
    opacity: open ? 1 : 0,
    pointerEvents: open ? 'auto' : 'none',
    transition: 'opacity 0.3s ease-in-out',
    zIndex: zIndex - 1,
  };

  const handleClose = () => {
    selectComponent(undefined);
  };

  return (
    <>
      {/* 遮罩层 */}
      <div
        style={maskStyle}
        onClick={() => {
          if (maskClosable) handleClose();
        }}
      />

      {/* 抽屉 */}
      <aside style={drawerStyle} onClick={(e) => e.stopPropagation()}>
        {/* 顶部栏 */}
        <div style={{
          height: 56,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 12px',
          borderBottom: '1px solid #e8e8e8',
          background: '#fff'
        }}>
          <div style={{ fontSize: 16, fontWeight: 600, color: '#333' }}>属性编辑</div>
          <button onClick={handleClose} style={{
            border: 'none',
            background: 'transparent',
            cursor: 'pointer',
            color: '#666',
            fontSize: 14
          }}>关闭 ✕</button>
        </div>
        {/* 内容：复用现有 PropertyPanel */}
        <div style={{ flex: 1, minHeight: 0 }}>
          <PropertyPanel width={width} />
        </div>
      </aside>
    </>
  );
};

