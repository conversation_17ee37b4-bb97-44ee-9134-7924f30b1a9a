import React, { useState } from 'react';
import { ComponentPanel } from './ComponentPanel/ComponentPanel';
import { Canvas } from './Canvas/Canvas';
import { PropertyPanel } from './PropertyPanel/PropertyPanel';
import { Toolbar } from './Toolbar/Toolbar';
import { useDesigner } from '../context/DesignerContext';

export interface DesignerProps {
  style?: React.CSSProperties;
  className?: string;
}

export const Designer: React.FC<DesignerProps> = ({ style, className }) => {
  const { canvasState } = useDesigner();
  const [isComponentsCollapsed, setIsComponentsCollapsed] = useState(false);
  const [showPropertyPanel, setShowPropertyPanel] = useState(false);

  // 当选中组件时自动显示属性面板
  React.useEffect(() => {
    if (canvasState.selectedComponentId) {
      setShowPropertyPanel(true);
    } else {
      setShowPropertyPanel(false);
    }
  }, [canvasState.selectedComponentId]);

  const defaultStyle: React.CSSProperties = {
    height: '100vh',
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: '#f5f5f5',
    fontFamily: 'Inter, sans-serif',
    ...style,
  };

  return (
    <div className={`lowcode-designer ${className || ''}`} style={defaultStyle}>
      {/* 顶部工具栏 */}
      <Toolbar />
      
      {/* 主体区域 */}
      <div style={{ 
        flex: 1, 
        display: 'flex', 
        overflow: 'hidden',
        position: 'relative'
      }}>
        {/* 左侧组件面板 */}
        <div 
          style={{
            width: isComponentsCollapsed ? '0px' : '264px',
            transition: 'width 0.3s ease',
            position: 'relative',
            flexShrink: 0,
          }}
        >
          <ComponentPanel 
            width={264}
            isCollapsed={isComponentsCollapsed}
            onToggleCollapse={() => setIsComponentsCollapsed(!isComponentsCollapsed)}
          />
        </div>

        {/* 中央画布区域 */}
        <div style={{ 
          flex: 1, 
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          backgroundColor: '#f0f2f5'
        }}>
          <Canvas 
            onComponentSelect={() => setShowPropertyPanel(true)}
            onCanvasClick={() => setShowPropertyPanel(false)}
          />
        </div>

        {/* 右侧属性面板遮罩 */}
        {showPropertyPanel && (
          <div
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
              zIndex: 30,
              transition: 'opacity 0.3s ease',
            }}
            onClick={() => setShowPropertyPanel(false)}
          />
        )}

        {/* 右侧属性面板 */}
        <div
          style={{
            position: 'fixed',
            top: 0,
            right: 0,
            height: '100%',
            width: '384px',
            transform: showPropertyPanel ? 'translateX(0)' : 'translateX(100%)',
            transition: 'transform 0.3s ease',
            zIndex: 40,
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <PropertyPanel 
            width={384}
            onClose={() => setShowPropertyPanel(false)}
          />
        </div>
      </div>
    </div>
  );
};
