# 低代码平台设计器

基于React的可视化低代码设计器，实现了像素级还原的设计稿界面。

## 特性

- 🎨 **像素级还原** - 完全按照设计稿实现的界面布局和样式
- 🧩 **组件面板** - 可折叠的左侧组件库，支持分类浏览和拖拽
- 🖼️ **可视化画布** - 中央设计区域，支持组件选择和交互
- ⚙️ **属性面板** - 右侧滑出式属性编辑面板
- 🔧 **工具栏** - 顶部工具栏，支持预览模式切换
- 📱 **响应式设计** - 适配不同屏幕尺寸

## 安装

```bash
npm install @lowcode/designer
```

## 快速开始

### 基础用法

```tsx
import React from 'react';
import { DesignerApp } from '@lowcode/designer';

function App() {
  return <DesignerApp />;
}

export default App;
```

### 自定义初始Schema

```tsx
import React from 'react';
import { DesignerApp } from '@lowcode/designer';
import { PageSchema } from '@lowcode/renderer';

const customSchema: PageSchema = {
  id: 'custom_page',
  title: '自定义页面',
  components: [
    {
      id: 'header',
      type: 'TopNavigation',
      props: {
        brand: '我的应用',
        items: [
          { key: 'home', label: '首页', active: true },
          { key: 'about', label: '关于' }
        ]
      }
    }
  ],
  apis: [],
  theme: {},
  layout: { type: 'admin', header: true, sidebar: false, footer: false }
};

function App() {
  return <DesignerApp initialSchema={customSchema} />;
}

export default App;
```

### 使用独立组件

```tsx
import React from 'react';
import { 
  DesignerProvider, 
  Designer, 
  ComponentPanel, 
  Canvas, 
  PropertyPanel, 
  Toolbar 
} from '@lowcode/designer';

function CustomDesigner() {
  return (
    <DesignerProvider>
      <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
        <Toolbar />
        <div style={{ flex: 1, display: 'flex' }}>
          <ComponentPanel width={280} />
          <Canvas />
          <PropertyPanel width={320} />
        </div>
      </div>
    </DesignerProvider>
  );
}
```

## 组件API

### DesignerApp

主应用组件，包含完整的设计器功能。

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| initialSchema | PageSchema | 示例Schema | 初始页面Schema |

### Designer

设计器主组件，需要在DesignerProvider内使用。

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| style | CSSProperties | - | 自定义样式 |
| className | string | - | 自定义类名 |

### ComponentPanel

组件面板，显示可用的组件库。

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| width | number | 264 | 面板宽度 |
| isCollapsed | boolean | false | 是否折叠 |
| onToggleCollapse | () => void | - | 折叠切换回调 |

### Canvas

画布组件，用于显示和编辑页面内容。

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| onComponentSelect | () => void | - | 组件选择回调 |
| onCanvasClick | () => void | - | 画布点击回调 |

### PropertyPanel

属性面板，用于编辑选中组件的属性。

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| width | number | 384 | 面板宽度 |
| onClose | () => void | - | 关闭回调 |

### Toolbar

工具栏组件，提供全局操作。

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| height | number | 48 | 工具栏高度 |

## 设计理念

本设计器严格按照设计稿进行像素级还原，包括：

- **精确的布局尺寸** - 所有间距、尺寸都与设计稿保持一致
- **一致的视觉风格** - 颜色、字体、圆角等视觉元素完全匹配
- **流畅的交互体验** - hover效果、过渡动画等交互细节
- **响应式适配** - 在不同屏幕尺寸下保持良好的用户体验

## 示例

查看 `examples/designer-demo.html` 文件可以看到完整的设计器界面效果。

## 开发

```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 构建
npm run build

# 类型检查
npm run type-check
```

## 许可证

MIT
