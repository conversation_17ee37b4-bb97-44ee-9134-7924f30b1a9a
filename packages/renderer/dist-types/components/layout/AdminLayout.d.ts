import React from 'react';
export interface AdminLayoutProps {
    header?: React.ReactNode;
    sidebar?: React.ReactNode;
    footer?: React.ReactNode;
    children?: React.ReactNode;
    sidebarWidth?: number;
    headerHeight?: number;
    footerHeight?: number;
    sidebarCollapsed?: boolean;
    sidebarCollapsible?: boolean;
    style?: React.CSSProperties;
    className?: string;
    onSidebarToggle?: (collapsed: boolean) => void;
}
export declare const AdminLayout: React.FC<AdminLayoutProps>;
//# sourceMappingURL=AdminLayout.d.ts.map