import { ComponentSchema, ComponentMeta } from '../types';

// 生成唯一ID
export function generateId(prefix: string = 'comp'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 深度克隆对象
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as any;
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as any;
  }

  if (typeof obj === 'object') {
    const cloned = {} as any;
    Object.keys(obj).forEach(key => {
      cloned[key] = deepClone((obj as any)[key]);
    });
    return cloned;
  }

  return obj;
}

// 查找组件
export function findComponent(schema: ComponentSchema[], id: string): ComponentSchema | null {
  for (const component of schema) {
    if (component.id === id) {
      return component;
    }

    if (component.children) {
      const found = findComponent(component.children, id);
      if (found) {
        return found;
      }
    }
  }

  return null;
}

// 查找父组件
export function findParentComponent(schema: ComponentSchema[], childId: string): ComponentSchema | null {
  for (const component of schema) {
    if (component.children) {
      for (const child of component.children) {
        if (child.id === childId) {
          return component;
        }
      }

      const found = findParentComponent(component.children, childId);
      if (found) {
        return found;
      }
    }
  }

  return null;
}

// 移除组件
export function removeComponent(schema: ComponentSchema[], id: string): ComponentSchema[] {
  return schema.filter(component => {
    if (component.id === id) {
      return false;
    }

    if (component.children) {
      component.children = removeComponent(component.children, id);
    }

    return true;
  });
}

// 更新组件
export function updateComponent(
  schema: ComponentSchema[],
  id: string,
  updates: Partial<ComponentSchema>
): ComponentSchema[] {
  return schema.map(component => {
    if (component.id === id) {
      return { ...component, ...updates };
    }

    if (component.children) {
      return {
        ...component,
        children: updateComponent(component.children, id, updates)
      };
    }

    return component;
  });
}

// 验证组件Schema
export function validateComponentSchema(schema: ComponentSchema): string[] {
  const errors: string[] = [];

  if (!schema.id) {
    errors.push('Component id is required');
  }

  if (!schema.type) {
    errors.push('Component type is required');
  }

  if (schema.children) {
    schema.children.forEach((child, index) => {
      const childErrors = validateComponentSchema(child);
      childErrors.forEach(error => {
        errors.push(`Child ${index}: ${error}`);
      });
    });
  }

  return errors;
}

// 创建默认组件Schema
export function createDefaultComponentSchema(type: string, meta?: ComponentMeta): ComponentSchema {
  return {
    id: generateId(type),
    type,
    props: meta?.defaultProps || {},
    children: [],
    events: {},
    style: {},
    className: ''
  };
}

// 扁平化组件树
export function flattenComponentTree(schema: ComponentSchema[]): ComponentSchema[] {
  const result: ComponentSchema[] = [];

  function traverse(components: ComponentSchema[]) {
    components.forEach(component => {
      result.push(component);
      if (component.children) {
        traverse(component.children);
      }
    });
  }

  traverse(schema);
  return result;
}

// 获取组件路径
export function getComponentPath(schema: ComponentSchema[], id: string): number[] {
  const path: number[] = [];

  function findPath(components: ComponentSchema[], currentPath: number[]): boolean {
    for (let i = 0; i < components.length; i++) {
      const component = components[i];
      const newPath = [...currentPath, i];

      if (component.id === id) {
        path.push(...newPath);
        return true;
      }

      if (component.children && findPath(component.children, newPath)) {
        return true;
      }
    }

    return false;
  }

  findPath(schema, []);
  return path;
}
