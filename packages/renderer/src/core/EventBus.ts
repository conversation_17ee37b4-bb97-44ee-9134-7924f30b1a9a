export class EventBus {
  private events: Map<string, Function[]> = new Map();

  // 监听事件
  on(event: string, handler: Function): void {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(handler);
  }

  // 取消监听事件
  off(event: string, handler: Function): void {
    const handlers = this.events.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }

      // 如果没有处理器了，删除事件
      if (handlers.length === 0) {
        this.events.delete(event);
      }
    }
  }



  // 只监听一次事件
  once(event: string, handler: Function): void {
    const onceHandler = (...args: any[]) => {
      handler(...args);
      this.off(event, onceHandler);
    };
    this.on(event, onceHandler);
  }

  // 获取事件的监听器数量
  listenerCount(event: string): number {
    const handlers = this.events.get(event);
    return handlers ? handlers.length : 0;
  }

  // 获取所有事件名称
  eventNames(): string[] {
    return Array.from(this.events.keys());
  }

  // 移除所有监听器
  removeAllListeners(event?: string): void {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
  }

  // 获取事件的所有监听器
  listeners(event: string): Function[] {
    return this.events.get(event) || [];
  }

  // 支持通配符的事件监听
  onWildcard(pattern: string, handler: Function): void {
    // 将通配符模式转换为正则表达式
    const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$');

    const wildcardHandler = (event: string, ...args: any[]) => {
      if (regex.test(event)) {
        handler(event, ...args);
      }
    };

    // 存储通配符处理器的映射关系
    (wildcardHandler as any).__pattern = pattern;
    (wildcardHandler as any).__originalHandler = handler;

    this.on('*', wildcardHandler);
  }

  // 移除通配符监听器
  offWildcard(pattern: string, handler: Function): void {
    const handlers = this.events.get('*') || [];
    const toRemove = handlers.filter(h =>
      (h as any).__pattern === pattern && (h as any).__originalHandler === handler
    );

    toRemove.forEach(h => this.off('*', h));
  }

  // 增强的emit方法，支持通配符
  emit(event: string, ...args: any[]): void {
    // 触发具体事件的监听器
    const handlers = this.events.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args);
        } catch (error) {
          console.error(`Error in event handler for "${event}":`, error);
        }
      });
    }

    // 触发通配符监听器
    const wildcardHandlers = this.events.get('*');
    if (wildcardHandlers) {
      wildcardHandlers.forEach(handler => {
        try {
          handler(event, ...args);
        } catch (error) {
          console.error(`Error in wildcard event handler for "${event}":`, error);
        }
      });
    }
  }

  // 事件命名空间支持
  namespace(ns: string) {
    return {
      on: (event: string, handler: Function) => {
        this.on(`${ns}:${event}`, handler);
      },
      off: (event: string, handler: Function) => {
        this.off(`${ns}:${event}`, handler);
      },
      emit: (event: string, ...args: any[]) => {
        this.emit(`${ns}:${event}`, ...args);
      },
      once: (event: string, handler: Function) => {
        this.once(`${ns}:${event}`, handler);
      }
    };
  }

  // 获取所有事件的统计信息
  getStats() {
    const stats: Record<string, number> = {};
    this.events.forEach((handlers, event) => {
      stats[event] = handlers.length;
    });
    return stats;
  }

  // 调试模式：记录所有事件
  enableDebug() {
    this.on('*', (event: string, ...args: any[]) => {
      console.log(`[EventBus] ${event}:`, args);
    });
  }
}
