import { ThemeConfig } from '../types';

export class ThemeManager {
  private theme: ThemeConfig;
  private defaultTheme: ThemeConfig = {
    primaryColor: '#1890ff',
    backgroundColor: '#ffffff',
    textColor: '#000000',
    borderColor: '#d9d9d9',
    borderRadius: 4,
    fontSize: 14,
  };

  // 预设主题
  private presetThemes: Record<string, ThemeConfig> = {
    light: {
      primaryColor: '#1890ff',
      backgroundColor: '#ffffff',
      textColor: '#000000',
      borderColor: '#d9d9d9',
      borderRadius: 4,
      fontSize: 14,
    },
    dark: {
      primaryColor: '#177ddc',
      backgroundColor: '#141414',
      textColor: '#ffffff',
      borderColor: '#434343',
      borderRadius: 4,
      fontSize: 14,
    },
    blue: {
      primaryColor: '#1890ff',
      backgroundColor: '#f0f8ff',
      textColor: '#001529',
      borderColor: '#91d5ff',
      borderRadius: 6,
      fontSize: 14,
    },
    green: {
      primaryColor: '#52c41a',
      backgroundColor: '#f6ffed',
      textColor: '#000000',
      borderColor: '#b7eb8f',
      borderRadius: 4,
      fontSize: 14,
    }
  };

  constructor(initialTheme?: ThemeConfig) {
    this.theme = { ...this.defaultTheme, ...initialTheme };
    this.applyTheme();
  }



  // 获取主题
  getTheme(): ThemeConfig {
    return { ...this.theme };
  }

  // 获取主题变量
  getThemeVar(key: keyof ThemeConfig): any {
    return this.theme[key];
  }

  // 重置为默认主题
  resetTheme() {
    this.theme = { ...this.defaultTheme };
    this.applyTheme();
  }

  // 应用主题到CSS变量
  private applyTheme() {
    const root = document.documentElement;

    if (this.theme.primaryColor) {
      root.style.setProperty('--lowcode-primary-color', this.theme.primaryColor);
    }

    if (this.theme.backgroundColor) {
      root.style.setProperty('--lowcode-background-color', this.theme.backgroundColor);
    }

    if (this.theme.textColor) {
      root.style.setProperty('--lowcode-text-color', this.theme.textColor);
    }

    if (this.theme.borderColor) {
      root.style.setProperty('--lowcode-border-color', this.theme.borderColor);
    }

    if (this.theme.borderRadius !== undefined) {
      root.style.setProperty('--lowcode-border-radius', `${this.theme.borderRadius}px`);
    }

    if (this.theme.fontSize !== undefined) {
      root.style.setProperty('--lowcode-font-size', `${this.theme.fontSize}px`);
    }
  }

  // 获取页面样式
  getPageStyle(): React.CSSProperties {
    return {
      backgroundColor: this.theme.backgroundColor,
      color: this.theme.textColor,
      fontSize: this.theme.fontSize,
      minHeight: '100vh',
    };
  }

  // 获取组件样式
  getComponentStyle(customStyle?: React.CSSProperties): React.CSSProperties {
    return {
      borderColor: this.theme.borderColor,
      borderRadius: this.theme.borderRadius,
      ...customStyle,
    };
  }

  // 生成主题CSS
  generateThemeCSS(): string {
    return `
      :root {
        --lowcode-primary-color: ${this.theme.primaryColor};
        --lowcode-background-color: ${this.theme.backgroundColor};
        --lowcode-text-color: ${this.theme.textColor};
        --lowcode-border-color: ${this.theme.borderColor};
        --lowcode-border-radius: ${this.theme.borderRadius}px;
        --lowcode-font-size: ${this.theme.fontSize}px;
      }

      .lowcode-page {
        background-color: var(--lowcode-background-color);
        color: var(--lowcode-text-color);
        font-size: var(--lowcode-font-size);
        min-height: 100vh;
      }

      .lowcode-component {
        border-color: var(--lowcode-border-color);
        border-radius: var(--lowcode-border-radius);
      }
    `;
  }

  // 导出主题配置
  exportTheme(): string {
    return JSON.stringify(this.theme, null, 2);
  }

  // 导入主题配置
  importTheme(themeJson: string) {
    try {
      const theme = JSON.parse(themeJson);
      this.setTheme(theme);
    } catch (error) {
      console.error('Failed to import theme:', error);
    }
  }

  // 应用预设主题
  applyPresetTheme(presetName: string) {
    const preset = this.presetThemes[presetName];
    if (preset) {
      this.setTheme(preset);
    } else {
      console.warn(`Preset theme "${presetName}" not found`);
    }
  }

  // 获取所有预设主题名称
  getPresetThemeNames(): string[] {
    return Object.keys(this.presetThemes);
  }

  // 获取预设主题
  getPresetTheme(name: string): ThemeConfig | undefined {
    return this.presetThemes[name];
  }

  // 添加自定义预设主题
  addPresetTheme(name: string, theme: ThemeConfig) {
    this.presetThemes[name] = theme;
  }

  // 移除预设主题
  removePresetTheme(name: string) {
    delete this.presetThemes[name];
  }

  // 获取当前主题与预设主题的差异
  getThemeDiff(presetName: string): Partial<ThemeConfig> {
    const preset = this.presetThemes[presetName];
    if (!preset) {
      return {};
    }

    const diff: Partial<ThemeConfig> = {};
    Object.keys(this.theme).forEach(key => {
      const themeKey = key as keyof ThemeConfig;
      if (this.theme[themeKey] !== preset[themeKey]) {
        (diff as any)[themeKey] = this.theme[themeKey];
      }
    });

    return diff;
  }

  // 创建主题变体（基于当前主题调整）
  createThemeVariant(adjustments: Partial<ThemeConfig>): ThemeConfig {
    return { ...this.theme, ...adjustments };
  }

  // 获取主题的CSS变量映射
  getCSSVariables(): Record<string, string> {
    return {
      '--lowcode-primary-color': this.theme.primaryColor || '',
      '--lowcode-background-color': this.theme.backgroundColor || '',
      '--lowcode-text-color': this.theme.textColor || '',
      '--lowcode-border-color': this.theme.borderColor || '',
      '--lowcode-border-radius': `${this.theme.borderRadius || 0}px`,
      '--lowcode-font-size': `${this.theme.fontSize || 14}px`,
    };
  }

  // 批量设置CSS变量
  setCSSVariables(variables: Record<string, string>) {
    const root = document.documentElement;
    Object.entries(variables).forEach(([key, value]) => {
      root.style.setProperty(key, value);
    });
  }

  // 监听主题变化
  private themeChangeListeners: Array<(theme: ThemeConfig) => void> = [];

  onThemeChange(listener: (theme: ThemeConfig) => void) {
    this.themeChangeListeners.push(listener);
  }

  offThemeChange(listener: (theme: ThemeConfig) => void) {
    const index = this.themeChangeListeners.indexOf(listener);
    if (index > -1) {
      this.themeChangeListeners.splice(index, 1);
    }
  }

  // 触发主题变化事件
  private notifyThemeChange() {
    this.themeChangeListeners.forEach(listener => {
      try {
        listener(this.getTheme());
      } catch (error) {
        console.error('Error in theme change listener:', error);
      }
    });
  }

  // 重写setTheme方法以支持事件通知
  setTheme(theme: ThemeConfig) {
    this.theme = { ...this.theme, ...theme };
    this.applyTheme();
    this.notifyThemeChange();
  }
}
