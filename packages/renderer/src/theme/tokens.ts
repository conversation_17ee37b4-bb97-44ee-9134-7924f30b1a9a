// 统一视觉 Tokens（可被 ThemeManager 的 CSS 变量覆盖）
// ToB 轻量、中性色为主，品牌蓝偏理性

export const tokens = {
  color: {
    // 品牌与文本/背景/描边
    brand: 'var(--lowcode-primary-color, #1677ff)',
    text: 'var(--lowcode-text-color, #111827)',
    bg: 'var(--lowcode-background-color, #ffffff)',
    border: 'var(--lowcode-border-color, #e5e7eb)',

    // 中性色与辅助色阶
    gray25: '#f8fafc',
    gray50: '#f9fafb',
    gray100: '#f3f4f6',
    gray200: '#e5e7eb',
    gray250: '#eef2f7',

    blue50: '#eff6ff',
    blue100: '#dbeafe',
  },
  radius: {
    sm: 6,
    md: 8,
    lg: 12,
    pill: 9999,
  },
  shadow: {
    xs: '0 1px 2px rgba(0,0,0,0.04)',
    sm: '0 2px 8px rgba(0,0,0,0.08)',
    md: '0 10px 24px rgba(0,0,0,0.12)',
  },
  spacing: {
    xs: 4,
    sm: 6,
    md: 8,
    lg: 12,
    xl: 16,
  },
} as const;

// 聚焦环（与品牌蓝匹配的轻量阴影）
export const focusRing = (rgba: string = 'rgba(59,130,246,0.15)') => `0 0 0 3px ${rgba}`;

