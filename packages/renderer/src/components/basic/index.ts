import React from 'react';
import { tokens, focusRing } from '../../theme/tokens';
import { ComponentMeta } from '../../types';

// 基础文本组件
export const Text: React.FC<any> = ({ children, ...props }) => {
  return React.createElement('span', props, children);
};

// 基础按钮组件（浅色视觉风格）
export const Button: React.FC<any> = ({ children, style, disabled, onMouseEnter, onMouseLeave, ...props }) => {
  const baseStyle: React.CSSProperties = {
    padding: '0 12px',
    height: '32px',
    lineHeight: '32px',
    backgroundColor: tokens.color.bg,
    color: '#374151',
    border: `1px solid ${tokens.color.border}`,
    borderRadius: `${tokens.radius.md}px`,
    cursor: disabled ? 'not-allowed' : 'pointer',
    fontSize: '14px',
    transition: 'all 0.2s ease',
    opacity: disabled ? 0.6 : 1,
    boxShadow: tokens.shadow.xs,
    ...style
  };

  const handleMouseEnter = (e: any) => {
    if (!disabled) {
      (e.currentTarget as HTMLElement).style.backgroundColor = tokens.color.gray100;
    }
    onMouseEnter?.(e);
  };

  const handleMouseLeave = (e: any) => {
    if (!disabled) {
      (e.currentTarget as HTMLElement).style.backgroundColor = tokens.color.bg;
    }
    onMouseLeave?.(e);
  };

  return React.createElement(
    'button',
    { ...props, disabled, style: baseStyle, onMouseEnter: handleMouseEnter, onMouseLeave: handleMouseLeave },
    children
  );
};

// 基础输入框组件（浅色视觉风格）
export const Input: React.FC<any> = ({ style, onFocus, onBlur, ...props }) => {
  const inputStyle: React.CSSProperties = {
    height: '32px',
    padding: '4px 10px',
    border: `1px solid ${tokens.color.border}`,
    borderRadius: `${tokens.radius.md}px`,
    outline: 'none',
    transition: 'all 0.2s',
    ...style,
  };
  return React.createElement('input', {
    ...props,
    style: inputStyle,
    onFocus: (e: any) => {
      (e.currentTarget as HTMLElement).style.borderColor = tokens.color.brand;
      (e.currentTarget as HTMLElement).style.boxShadow = focusRing();
      onFocus?.(e);
    },
    onBlur: (e: any) => {
      (e.currentTarget as HTMLElement).style.borderColor = tokens.color.border;
      (e.currentTarget as HTMLElement).style.boxShadow = 'none';
      onBlur?.(e);
    }
  });
};

// 基础容器组件
export const Container: React.FC<any> = ({ children, ...props }) => {
  return React.createElement('div', props, children);
};

// 基础图片组件
export const Image: React.FC<any> = (props) => {
  return React.createElement('img', props);
};

// 基础链接组件
export const Link: React.FC<any> = ({ children, ...props }) => {
  return React.createElement('a', props, children);
};

// 组件元数据定义
export const basicComponentMetas: ComponentMeta[] = [
  {
    type: 'Text',
    name: '文本',
    description: '基础文本组件',
    category: 'basic',
    icon: 'text',
    props: [
      {
        name: 'children',
        type: 'string',
        required: true,
        default: '文本内容',
        description: '文本内容'
      },
      {
        name: 'style',
        type: 'object',
        description: '样式'
      },
      {
        name: 'className',
        type: 'string',
        description: 'CSS类名'
      }
    ],
    defaultProps: {
      children: '文本内容'
    }
  },
  {
    type: 'Button',
    name: '按钮',
    description: '基础按钮组件',
    category: 'basic',
    icon: 'button',
    props: [
      {
        name: 'children',
        type: 'string',
        required: true,
        default: '按钮',
        description: '按钮文本'
      },
      {
        name: 'type',
        type: 'string',
        default: 'button',
        description: '按钮类型',
        options: [
          { label: '普通按钮', value: 'button' },
          { label: '提交按钮', value: 'submit' },
          { label: '重置按钮', value: 'reset' }
        ]
      },
      {
        name: 'disabled',
        type: 'boolean',
        default: false,
        description: '是否禁用'
      }
    ],
    events: [
      {
        name: 'onClick',
        description: '点击事件',
        params: [
          { name: 'event', type: 'MouseEvent', description: '鼠标事件对象' }
        ]
      }
    ],
    defaultProps: {
      children: '按钮',
      type: 'button'
    }
  },
  {
    type: 'Input',
    name: '输入框',
    description: '基础输入框组件',
    category: 'basic',
    icon: 'input',
    props: [
      {
        name: 'type',
        type: 'string',
        default: 'text',
        description: '输入框类型',
        options: [
          { label: '文本', value: 'text' },
          { label: '密码', value: 'password' },
          { label: '邮箱', value: 'email' },
          { label: '数字', value: 'number' }
        ]
      },
      {
        name: 'placeholder',
        type: 'string',
        description: '占位符文本'
      },
      {
        name: 'value',
        type: 'string',
        description: '输入值'
      },
      {
        name: 'disabled',
        type: 'boolean',
        default: false,
        description: '是否禁用'
      }
    ],
    events: [
      {
        name: 'onChange',
        description: '值变化事件',
        params: [
          { name: 'event', type: 'ChangeEvent', description: '变化事件对象' }
        ]
      },
      {
        name: 'onFocus',
        description: '获得焦点事件'
      },
      {
        name: 'onBlur',
        description: '失去焦点事件'
      }
    ],
    defaultProps: {
      type: 'text',
      placeholder: '请输入内容'
    }
  },
  {
    type: 'Container',
    name: '容器',
    description: '基础容器组件',
    category: 'layout',
    icon: 'container',
    props: [
      {
        name: 'style',
        type: 'object',
        description: '样式'
      },
      {
        name: 'className',
        type: 'string',
        description: 'CSS类名'
      }
    ],
    defaultProps: {
      // 默认不强加任何视觉样式，交由业务组件或布局组件定义
      style: {}
    }
  },
  {
    type: 'Image',
    name: '图片',
    description: '基础图片组件',
    category: 'basic',
    icon: 'image',
    props: [
      {
        name: 'src',
        type: 'string',
        required: true,
        description: '图片地址'
      },
      {
        name: 'alt',
        type: 'string',
        description: '替代文本'
      },
      {
        name: 'width',
        type: 'string',
        description: '宽度'
      },
      {
        name: 'height',
        type: 'string',
        description: '高度'
      }
    ],
    defaultProps: {
      src: 'https://via.placeholder.com/200x150',
      alt: '图片'
    }
  },
  {
    type: 'Link',
    name: '链接',
    description: '基础链接组件',
    category: 'basic',
    icon: 'link',
    props: [
      {
        name: 'href',
        type: 'string',
        required: true,
        description: '链接地址'
      },
      {
        name: 'target',
        type: 'string',
        default: '_self',
        description: '打开方式',
        options: [
          { label: '当前窗口', value: '_self' },
          { label: '新窗口', value: '_blank' }
        ]
      },
      {
        name: 'children',
        type: 'string',
        required: true,
        default: '链接文本',
        description: '链接文本'
      }
    ],
    defaultProps: {
      href: '#',
      target: '_self',
      children: '链接文本'
    }
  }
];

// 组件映射
export const basicComponents = {
  Text,
  Button,
  Input,
  Container,
  Image,
  Link
};
