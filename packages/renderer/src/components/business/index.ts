import { ComponentMeta } from '../../types';
import { TopNavigation } from './TopNavigation';
import { SidebarTreeView } from './SidebarTreeView';
import { TableViewWithSearch } from './TableViewWithSearch';
import { StatusBar } from './StatusBar';
import { ComponentLibrarySidebar } from './ComponentLibrarySidebar';

// 导出组件
export { TopNavigation, SidebarTreeView, TableViewWithSearch, StatusBar, ComponentLibrarySidebar };

// TopNavigation组件元数据
export const topNavigationMeta: ComponentMeta = {
  type: 'TopNavigation',
  name: '顶部导航',
  description: '顶部导航组件，包含Logo、菜单、用户信息等',
  category: 'business',
  icon: 'navigation',
  props: [
    {
      name: 'logo',
      type: 'object',
      description: 'Logo配置',
      default: {
        text: '系统名称',
        href: '/'
      }
    },
    {
      name: 'menu',
      type: 'array',
      description: '主菜单配置',
      default: [
        { key: 'home', label: '首页', href: '/' },
        { key: 'about', label: '关于' }
      ]
    },
    {
      name: 'user',
      type: 'object',
      description: '用户信息配置',
      default: {
        name: '用户名',
        menu: [
          { key: 'profile', label: '个人资料' },
          { key: 'logout', label: '退出登录' }
        ]
      }
    },
    {
      name: 'actions',
      type: 'array',
      description: '操作按钮配置',
      default: []
    },
    {
      name: 'style',
      type: 'object',
      description: '自定义样式'
    },
    {
      name: 'className',
      type: 'string',
      description: 'CSS类名'
    }
  ],
  events: [
    {
      name: 'onMenuClick',
      description: '菜单点击事件',
      params: [
        { name: 'key', type: 'string', description: '菜单项key' }
      ]
    },
    {
      name: 'onUserMenuClick',
      description: '用户菜单点击事件',
      params: [
        { name: 'key', type: 'string', description: '菜单项key' }
      ]
    }
  ],
  defaultProps: {
    logo: {
      text: '低代码平台',
      href: '/'
    },
    menu: [
      { key: 'dashboard', label: '仪表板', href: '/dashboard' },
      { key: 'components', label: '组件库', href: '/components' },
      { key: 'templates', label: '模板', href: '/templates' }
    ],
    user: {
      name: '管理员',
      menu: [
        { key: 'profile', label: '个人资料' },
        { key: 'settings', label: '系统设置' },
        { key: 'logout', label: '退出登录' }
      ]
    },
    actions: [
      { key: 'help', label: '帮助', icon: '?' },
      { key: 'notification', label: '通知', icon: '🔔' }
    ]
  }
};

// SidebarTreeView组件元数据
export const sidebarTreeViewMeta: ComponentMeta = {
  type: 'SidebarTreeView',
  name: '侧边栏树形导航',
  description: '侧边栏树形导航组件，支持搜索和层级展开',
  category: 'business',
  icon: 'tree',
  props: [
    {
      name: 'data',
      type: 'array',
      required: true,
      description: '树形数据',
      default: []
    },
    {
      name: 'width',
      type: 'number',
      description: '宽度',
      default: 240
    },
    {
      name: 'height',
      type: 'string',
      description: '高度',
      default: '100%'
    },
    {
      name: 'searchable',
      type: 'boolean',
      description: '是否可搜索',
      default: true
    },
    {
      name: 'searchPlaceholder',
      type: 'string',
      description: '搜索框占位符',
      default: '搜索...'
    },
    {
      name: 'defaultExpandedKeys',
      type: 'array',
      description: '默认展开的节点',
      default: []
    },
    {
      name: 'defaultSelectedKeys',
      type: 'array',
      description: '默认选中的节点',
      default: []
    },
    {
      name: 'showIcon',
      type: 'boolean',
      description: '是否显示图标',
      default: true
    }
  ],
  events: [
    {
      name: 'onSelect',
      description: '节点选择事件',
      params: [
        { name: 'selectedKeys', type: 'array', description: '选中的节点key数组' },
        { name: 'node', type: 'object', description: '选中的节点对象' }
      ]
    },
    {
      name: 'onExpand',
      description: '节点展开事件',
      params: [
        { name: 'expandedKeys', type: 'array', description: '展开的节点key数组' },
        { name: 'node', type: 'object', description: '操作的节点对象' }
      ]
    },
    {
      name: 'onSearch',
      description: '搜索事件',
      params: [
        { name: 'value', type: 'string', description: '搜索值' }
      ]
    }
  ],
  defaultProps: {
    data: [
      {
        key: 'dashboard',
        title: '仪表板',
        icon: '📊',
        href: '/dashboard'
      },
      {
        key: 'components',
        title: '组件管理',
        icon: '🧩',
        children: [
          { key: 'basic', title: '基础组件', icon: '🔧', href: '/components/basic' },
          { key: 'business', title: '业务组件', icon: '💼', href: '/components/business' },
          { key: 'custom', title: '自定义组件', icon: '⚙️', href: '/components/custom' }
        ]
      },
      {
        key: 'pages',
        title: '页面管理',
        icon: '📄',
        children: [
          { key: 'list', title: '页面列表', icon: '📋', href: '/pages/list' },
          { key: 'templates', title: '页面模板', icon: '📝', href: '/pages/templates' }
        ]
      },
      {
        key: 'settings',
        title: '系统设置',
        icon: '⚙️',
        children: [
          { key: 'users', title: '用户管理', icon: '👥', href: '/settings/users' },
          { key: 'roles', title: '角色管理', icon: '🔐', href: '/settings/roles' },
          { key: 'system', title: '系统配置', icon: '🔧', href: '/settings/system' }
        ]
      }
    ],
    width: 240,
    height: '100%',
    searchable: true,
    searchPlaceholder: '搜索菜单...',
    showIcon: true
  }
};

// TableViewWithSearch组件元数据
export const tableViewWithSearchMeta: ComponentMeta = {
  type: 'TableViewWithSearch',
  name: '表格搜索视图',
  description: '集成搜索、工具栏、表格功能的复合组件',
  category: 'business',
  icon: 'table',
  props: [
    {
      name: 'columns',
      type: 'array',
      required: true,
      description: '表格列配置',
      default: []
    },
    {
      name: 'dataSource',
      type: 'array',
      required: true,
      description: '表格数据源',
      default: []
    },
    {
      name: 'loading',
      type: 'boolean',
      description: '加载状态',
      default: false
    },
    {
      name: 'searchFields',
      type: 'array',
      description: '搜索字段配置',
      default: []
    },
    {
      name: 'toolbarActions',
      type: 'array',
      description: '工具栏操作按钮',
      default: []
    },
    {
      name: 'rowActions',
      type: 'array',
      description: '行操作按钮',
      default: []
    },
    {
      name: 'pagination',
      type: 'object',
      description: '分页配置'
    },
    {
      name: 'rowSelection',
      type: 'object',
      description: '行选择配置'
    }
  ],
  events: [
    {
      name: 'onSearch',
      description: '搜索事件',
      params: [
        { name: 'searchValues', type: 'object', description: '搜索值对象' }
      ]
    },
    {
      name: 'onTableChange',
      description: '表格变化事件',
      params: [
        { name: 'pagination', type: 'object', description: '分页信息' },
        { name: 'filters', type: 'object', description: '筛选信息' },
        { name: 'sorter', type: 'object', description: '排序信息' }
      ]
    }
  ],
  defaultProps: {
    columns: [
      { key: 'id', title: 'ID', dataIndex: 'id', width: 80, sortable: true },
      { key: 'name', title: '名称', dataIndex: 'name', sortable: true },
      { key: 'status', title: '状态', dataIndex: 'status', filterable: true },
      { key: 'createTime', title: '创建时间', dataIndex: 'createTime', sortable: true }
    ],
    dataSource: [
      { id: 1, name: '示例数据1', status: '启用', createTime: '2024-01-01' },
      { id: 2, name: '示例数据2', status: '禁用', createTime: '2024-01-02' },
      { id: 3, name: '示例数据3', status: '启用', createTime: '2024-01-03' }
    ],
    searchFields: [
      { key: 'name', label: '名称', type: 'input', placeholder: '请输入名称' },
      {
        key: 'status',
        label: '状态',
        type: 'select',
        placeholder: '请选择状态',
        options: [
          { label: '启用', value: '启用' },
          { label: '禁用', value: '禁用' }
        ]
      },
      { key: 'createTime', label: '创建时间', type: 'date' }
    ],
    toolbarActions: [
      { key: 'add', label: '新增', type: 'primary', icon: '+' },
      { key: 'export', label: '导出', type: 'default', icon: '📤' },
      { key: 'import', label: '导入', type: 'default', icon: '📥' }
    ],
    rowActions: [
      { key: 'edit', label: '编辑', type: 'primary', icon: '✏️' },
      { key: 'delete', label: '删除', type: 'danger', icon: '🗑️' }
    ],
    pagination: {
      current: 1,
      pageSize: 10,
      total: 100,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: true
    },
    rowSelection: {
      type: 'checkbox'
    }
  }
};

// StatusBar组件元数据
export const statusBarMeta: ComponentMeta = {
  type: 'StatusBar',
  name: '状态栏',
  description: '底部状态栏组件，显示系统状态信息',
  category: 'business',
  icon: 'status',
  props: [
    {
      name: 'items',
      type: 'array',
      required: true,
      description: '状态项配置',
      default: []
    },
    {
      name: 'position',
      type: 'string',
      description: '定位方式',
      default: 'fixed',
      options: [
        { label: '固定定位', value: 'fixed' },
        { label: '相对定位', value: 'relative' }
      ]
    },
    {
      name: 'height',
      type: 'number',
      description: '高度',
      default: 32
    },
    {
      name: 'backgroundColor',
      type: 'string',
      description: '背景颜色',
      default: '#f5f5f5'
    },
    {
      name: 'textColor',
      type: 'string',
      description: '文字颜色',
      default: '#666666'
    },
    {
      name: 'borderTop',
      type: 'string',
      description: '顶部边框',
      default: '1px solid #e8e8e8'
    },
    {
      name: 'showSeparator',
      type: 'boolean',
      description: '显示分隔符',
      default: true
    }
  ],
  events: [
    {
      name: 'onItemClick',
      description: '状态项点击事件',
      params: [
        { name: 'item', type: 'object', description: '点击的状态项' }
      ]
    }
  ],
  defaultProps: {
    items: [
      { key: 'ready', label: '状态', value: '就绪', icon: '✅', color: '#52c41a' },
      { key: 'users', label: '在线用户', value: 128, icon: '👥', clickable: true },
      { key: 'memory', label: '内存使用', value: '45%', icon: '💾' },
      { key: 'time', label: '当前时间', value: new Date().toLocaleTimeString(), icon: '🕐' }
    ],
    position: 'fixed',
    height: 32,
    backgroundColor: '#f5f5f5',
    textColor: '#666666',
    borderTop: '1px solid #e8e8e8',
    showSeparator: true
  }
};

// 业务组件元数据集合
export const businessComponentMetas: ComponentMeta[] = [
  topNavigationMeta,
  sidebarTreeViewMeta,
  tableViewWithSearchMeta,
  statusBarMeta
];

// 业务组件映射
export const businessComponents = {
  TopNavigation,
  SidebarTreeView,
  TableViewWithSearch,
  StatusBar
};
