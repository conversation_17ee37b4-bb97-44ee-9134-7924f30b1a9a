import React from 'react';


export interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number | string;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: any, index: number) => React.ReactNode;
}

export interface TableAction {
  key: string;
  label: string;
  icon?: string;
  type?: 'primary' | 'default' | 'danger';
  disabled?: boolean;
  onClick?: (record: any, index: number) => void;
}

export interface ToolbarAction {
  key: string;
  label: string;
  icon?: string;
  type?: 'primary' | 'default' | 'danger';
  disabled?: boolean;
  onClick?: () => void;
}

export interface SearchField {
  key: string;
  label: string;
  type: 'input' | 'select' | 'date' | 'dateRange';
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
}

export interface TableViewWithSearchProps extends React.HTMLAttributes<HTMLDivElement> {
  columns: TableColumn[];
  dataSource: any[];
  loading?: boolean;
  searchFields?: SearchField[];
  toolbarActions?: ToolbarAction[];
  rowActions?: TableAction[];
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
    showTotal?: boolean;
  };
  rowSelection?: {
    type: 'checkbox' | 'radio';
    selectedRowKeys?: string[];
    onChange?: (selectedRowKeys: string[], selectedRows: any[]) => void;
  };
  style?: React.CSSProperties;
  className?: string;
  onSearch?: (searchValues: Record<string, any>) => void;
  onTableChange?: (pagination: any, filters: any, sorter: any) => void;
  onRow?: (record: any, index: number) => React.HTMLAttributes<HTMLTableRowElement>;
}

export const TableViewWithSearch: React.FC<TableViewWithSearchProps> = ({
  columns,
  dataSource,
  loading = false,
  searchFields = [],
  toolbarActions = [],
  rowActions = [],
  pagination,
  rowSelection,
  style,
  className,
  onSearch,
  onTableChange,
  onRow,
  ...rest
}) => {
  const [searchValues, setSearchValues] = React.useState<Record<string, any>>({});
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>(
    rowSelection?.selectedRowKeys || []
  );
  const [sortConfig, setSortConfig] = React.useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  // 处理搜索
  const handleSearch = () => {
    onSearch?.(searchValues);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchValues({});
    onSearch?.({});
  };

  // 处理搜索字段变化
  const handleSearchFieldChange = (key: string, value: any) => {
    const newValues = { ...searchValues, [key]: value };
    setSearchValues(newValues);
  };

  // 处理行选择
  const handleRowSelection = (keys: string[], rows: any[]) => {
    setSelectedRowKeys(keys);
    rowSelection?.onChange?.(keys, rows);
  };

  // 处理排序
  const handleSort = (column: TableColumn) => {
    if (!column.sortable) return;

    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig?.key === column.key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }

    setSortConfig({ key: column.key, direction });
    onTableChange?.(pagination, {}, { field: column.key, order: direction });
  };

  // 渲染搜索表单
  const renderSearchForm = () => {
    if (searchFields.length === 0) return null;

    return (
      <div
        style={{
          padding: '24px',
          backgroundColor: '#f9fafb',
          border: '1px solid #e5e7eb',
          borderRadius: '8px',
          marginBottom: '24px'
        }}
        data-component-id="searchForm"
      >
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(240px, 1fr))',
          gap: '24px',
          marginBottom: '24px'
        }}>
          {searchFields.map(field => (
            <div key={field.key}>
              <label style={{
                display: 'block',
                marginBottom: '4px',
                fontSize: '14px',
                fontWeight: '500',
                color: '#374151'
              }}>
                {field.label}
              </label>
              {field.type === 'input' && (
                <input
                  type="text"
                  placeholder={field.placeholder}
                  value={searchValues[field.key] || ''}
                  onChange={(e) => handleSearchFieldChange(field.key, e.target.value)}
                  style={{
                    width: '100%',
                    height: '40px',
                    padding: '8px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    transition: 'border-color 0.2s ease'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#2563eb';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                  }}
                />
              )}
              {field.type === 'select' && (
                <select
                  value={searchValues[field.key] || ''}
                  onChange={(e) => handleSearchFieldChange(field.key, e.target.value)}
                  style={{
                    width: '100%',
                    height: '40px',
                    padding: '8px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    transition: 'border-color 0.2s ease',
                    color: '#6b7280'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#2563eb';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                  }}
                >
                  <option value="">{field.placeholder || '请选择'}</option>
                  {field.options?.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              )}
              {field.type === 'date' && (
                <input
                  type="text"
                  placeholder={field.placeholder}
                  value={searchValues[field.key] || ''}
                  onChange={(e) => handleSearchFieldChange(field.key, e.target.value)}
                  style={{
                    width: '100%',
                    height: '40px',
                    padding: '8px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    outline: 'none',
                    transition: 'border-color 0.2s ease'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#2563eb';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                  }}
                />
              )}
            </div>
          ))}
        </div>

        <div style={{ display: 'flex', gap: '8px', justifyContent: 'flex-start' }}>
          <button
            onClick={handleSearch}
            style={{
              height: '40px',
              padding: '0 16px',
              backgroundColor: '#2563eb',
              color: '#ffffff',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#1d4ed8';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#2563eb';
            }}
          >
            搜索
          </button>
          <button
            onClick={handleReset}
            style={{
              height: '40px',
              padding: '0 16px',
              backgroundColor: '#ffffff',
              color: '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f3f4f6';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#ffffff';
            }}
          >
            重置
          </button>
        </div>
      </div>
    );
  };

  // 渲染工具栏
  const renderToolbar = () => {
    // 默认工具栏操作
    const defaultActions: ToolbarAction[] = [
      {
        key: 'export',
        label: '导出',
        icon: '📥',
        onClick: () => console.log('导出数据')
      },
      {
        key: 'refresh',
        label: '刷新',
        icon: '🔄',
        onClick: () => console.log('刷新数据')
      }
    ];

    const actions = toolbarActions.length > 0 ? toolbarActions : defaultActions;

    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '16px',
        marginBottom: '16px'
      }}>
        <div style={{ display: 'flex', gap: '16px' }}>
          {actions.map(action => (
            <button
              key={action.key}
              onClick={action.onClick}
              disabled={action.disabled}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
                height: '32px',
                padding: '0 8px',
                backgroundColor: 'transparent',
                color: '#2563eb',
                border: 'none',
                borderRadius: '4px',
                cursor: action.disabled ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                opacity: action.disabled ? 0.6 : 1,
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (!action.disabled) {
                  e.currentTarget.style.color = '#1d4ed8';
                }
              }}
              onMouseLeave={(e) => {
                if (!action.disabled) {
                  e.currentTarget.style.color = '#2563eb';
                }
              }}
            >
              {action.icon && <span style={{ fontSize: '16px' }}>{action.icon}</span>}
              {action.label}
            </button>
          ))}
        </div>

        {rowSelection && selectedRowKeys.length > 0 && (
          <div style={{ fontSize: '14px', color: '#6b7280' }}>
            已选择 {selectedRowKeys.length} 项
          </div>
        )}
      </div>
    );
  };

  // 渲染表格
  const renderTable = () => {
    return (
      <div
        style={{
          border: '1px solid #e5e7eb',
          borderRadius: '8px',
          overflow: 'hidden',
          backgroundColor: '#ffffff'
        }}
        data-component-id="table"
      >
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ backgroundColor: '#f9fafb' }}>
              {rowSelection && (
                <th style={{
                  padding: '8px 12px',
                  textAlign: 'left',
                  borderBottom: '1px solid #eef2f7',
                  width: '48px'
                }}>
                  <input
                    type={rowSelection.type === 'radio' ? 'radio' : 'checkbox'}
                    checked={selectedRowKeys.length === dataSource.length && dataSource.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        const allKeys = dataSource.map((_, index) => index.toString());
                        handleRowSelection(allKeys, dataSource);
                      } else {
                        handleRowSelection([], []);
                      }
                    }}
                  />
                </th>
              )}
              {columns.map(column => (
                <th
                  key={column.key}
                  style={{
                    padding: '12px 24px',
                    textAlign: column.align || 'left',
                    borderBottom: '1px solid #e5e7eb',
                    width: column.width,
                    cursor: column.sortable ? 'pointer' : 'default',
                    userSelect: 'none',
                    whiteSpace: 'nowrap',
                    fontSize: '12px',
                    fontWeight: '700',
                    color: '#374151',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}
                  onClick={() => handleSort(column)}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                    <span>{column.title}</span>
                    {column.sortable && (
                      <span style={{ fontSize: '12px' }}>
                        {sortConfig?.key === column.key ? (
                          sortConfig.direction === 'asc' ? '↑' : '↓'
                        ) : '↕'}
                      </span>
                    )}
                  </div>
                </th>
              ))}
              {rowActions.length > 0 && (
                <th style={{
                  padding: '8px 12px',
                  textAlign: 'center',
                  borderBottom: '1px solid #eef2f7'
                }}>
                  操作
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td
                  colSpan={columns.length + (rowSelection ? 1 : 0) + (rowActions.length > 0 ? 1 : 0)}
                  style={{
                    padding: '32px',
                    textAlign: 'center',
                    color: '#666',
                    backgroundColor: '#fff'
                  }}
                >
                  加载中...
                </td>
              </tr>
            ) : dataSource.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length + (rowSelection ? 1 : 0) + (rowActions.length > 0 ? 1 : 0)}
                  style={{
                    padding: '32px',
                    textAlign: 'center',
                    color: '#666',
                    backgroundColor: '#fff'
                  }}
                >
                  暂无数据
                </td>
              </tr>
            ) : (
              dataSource.map((record, index) => {
                const rowKey = index.toString();
                const isSelected = selectedRowKeys.includes(rowKey);
                const rowProps = onRow?.(record, index) || {};

                return (
                  <tr
                    key={rowKey}
                    {...rowProps}
                    style={{
                      backgroundColor: isSelected ? '#eef5ff' : 'transparent',
                      ...rowProps.style
                    }}
                    onMouseEnter={(e) => {
                      if (!isSelected) {
                        e.currentTarget.style.backgroundColor = '#f9fafb';
                      }
                      rowProps.onMouseEnter?.(e);
                    }}
                    onMouseLeave={(e) => {
                      if (!isSelected) {
                        e.currentTarget.style.backgroundColor = '#ffffff';
                      }
                      rowProps.onMouseLeave?.(e);
                    }}
                  >
                    {rowSelection && (
                      <td style={{
                        padding: '10px 12px',
                        borderBottom: '1px solid #eef2f7'
                      }}>
                        <input
                          type={rowSelection.type === 'radio' ? 'radio' : 'checkbox'}
                          checked={isSelected}
                          onChange={(e) => {
                            if (rowSelection.type === 'radio') {
                              handleRowSelection([rowKey], [record]);
                            } else {
                              if (e.target.checked) {
                                handleRowSelection([...selectedRowKeys, rowKey], [...selectedRowKeys.map(key => dataSource[parseInt(key)]), record]);
                              } else {
                                const newKeys = selectedRowKeys.filter(key => key !== rowKey);
                                handleRowSelection(newKeys, newKeys.map(key => dataSource[parseInt(key)]));
                              }
                            }
                          }}
                        />
                      </td>
                    )}
                    {columns.map(column => {
                      const value = record[column.dataIndex];
                      let cellContent = column.render ? column.render(value, record, index) : value;

                      // 特殊处理敏感等级列
                      if (column.dataIndex === 'sensitiveLevel' && value === '高敏感') {
                        cellContent = (
                          <span style={{
                            backgroundColor: '#fef2f2',
                            color: '#dc2626',
                            fontSize: '12px',
                            fontWeight: '500',
                            padding: '2px 10px',
                            borderRadius: '9999px',
                            border: '1px solid #fecaca'
                          }}>
                            高敏感
                          </span>
                        );
                      }

                      // 特殊处理操作列
                      if (column.dataIndex === 'actions') {
                        cellContent = (
                          <button
                            style={{
                              color: '#2563eb',
                              backgroundColor: 'transparent',
                              border: 'none',
                              cursor: 'pointer',
                              fontSize: '16px',
                              padding: '4px'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.color = '#1d4ed8';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.color = '#2563eb';
                            }}
                          >
                            ⋯
                          </button>
                        );
                      }

                      return (
                        <td
                          key={column.key}
                          style={{
                            padding: '16px 24px',
                            textAlign: column.align || 'left',
                            borderBottom: '1px solid #f3f4f6',
                            fontSize: '14px',
                            color: column.dataIndex === 'path' ? '#111827' : '#6b7280',
                            fontWeight: column.dataIndex === 'path' ? '500' : '400'
                          }}
                        >
                          {cellContent}
                        </td>
                      );
                    })}
                    {rowActions.length > 0 && (
                      <td style={{
                        padding: '10px 12px',
                        textAlign: 'center',
                        borderBottom: '1px solid #eef2f7'
                      }}>
                        <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
                          {rowActions.map(action => (
                            <button
                              key={action.key}
                              onClick={() => action.onClick?.(record, index)}
                              disabled={action.disabled}
                              style={{
                                height: '24px',
                                padding: '0 6px',
                                backgroundColor: action.type === 'primary' ? '#1677ff' :
                                                action.type === 'danger' ? '#ef4444' : 'transparent',
                                color: action.type === 'primary' || action.type === 'danger' ? '#ffffff' : '#1677ff',
                                border: action.type === 'primary' || action.type === 'danger' ? '1px solid transparent' : 'none',
                                borderRadius: '4px',
                                cursor: action.disabled ? 'not-allowed' : 'pointer',
                                fontSize: '12px',
                                opacity: action.disabled ? 0.6 : 1
                              }}
                            >
                              {action.icon && <span style={{ marginRight: '4px' }}>{action.icon}</span>}
                              {action.label}
                            </button>
                          ))}
                        </div>
                      </td>
                    )}
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    );
  };

  // 渲染分页
  const renderPagination = () => {
    if (!pagination) return null;

    const { current, pageSize, total, showSizeChanger, showQuickJumper, showTotal } = pagination;
    const totalPages = Math.ceil(total / pageSize);

    return (
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: '12px',
        padding: '12px 0'
      }}>
        {showTotal && (
          <div style={{ fontSize: '12px', color: '#666' }}>
            共 {total} 条记录
          </div>
        )}

        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {showSizeChanger && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ fontSize: '12px' }}>每页显示</span>
              <select
                value={pageSize}
                onChange={(e) => {
                  const newPageSize = parseInt(e.target.value);
                  onTableChange?.({ ...pagination, pageSize: newPageSize, current: 1 }, {}, {});
                }}
                style={{
                  height: '28px',
                  padding: '0 8px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px',
                  fontSize: '12px'
                }}
              >
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
              <span style={{ fontSize: '14px' }}>条</span>
            </div>
          )}

          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <button
              disabled={current <= 1}
              onClick={() => onTableChange?.({ ...pagination, current: current - 1 }, {}, {})}
              style={{
                height: '28px',
                padding: '0 8px',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                backgroundColor: '#ffffff',
                cursor: current <= 1 ? 'not-allowed' : 'pointer',
                opacity: current <= 1 ? 0.6 : 1
              }}
            >
              上一页
            </button>

            {showQuickJumper && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontSize: '12px' }}>跳至</span>
                <input
                  type="number"
                  min={1}
                  max={totalPages}
                  style={{
                    width: '56px',
                    height: '28px',
                    padding: '0 8px',
                    border: '1px solid #e5e7eb',
                    borderRadius: '4px',
                    fontSize: '12px'
                  }}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      const page = parseInt((e.target as HTMLInputElement).value);
                      if (page >= 1 && page <= totalPages) {
                        onTableChange?.({ ...pagination, current: page }, {}, {});
                      }
                    }
                  }}
                />
                <span style={{ fontSize: '14px' }}>页</span>
              </div>
            )}

            <span style={{ fontSize: '12px' }}>
              {current} / {totalPages}
            </span>

            <button
              disabled={current >= totalPages}
              onClick={() => onTableChange?.({ ...pagination, current: current + 1 }, {}, {})}
              style={{
                height: '28px',
                padding: '0 8px',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                backgroundColor: '#ffffff',
                cursor: current >= totalPages ? 'not-allowed' : 'pointer',
                opacity: current >= totalPages ? 0.6 : 1
              }}
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    );
  };

  const defaultStyle: React.CSSProperties = {
    backgroundColor: '#ffffff',
    borderRadius: 0,
    padding: '12px',
    ...style,
  };

  return (
    <div className={`lowcode-table-view-with-search ${className || ''}`} style={defaultStyle} {...rest}>
      {renderSearchForm()}
      {renderToolbar()}
      {renderTable()}
      {renderPagination()}
    </div>
  );
};
