# Low-Code Platform MVP

基于业务组件的低代码开发平台，包含设计器和渲染器两个核心模块。

## 项目结构

```
lowcode-platform/
├── packages/
│   ├── renderer/          # 渲染器引擎
│   │   ├── src/
│   │   ├── package.json
│   │   └── tsconfig.json
│   └── designer/          # 可视化设计器
│       ├── src/
│       ├── package.json
│       └── tsconfig.json
├── docs/                  # 文档
├── package.json           # 根配置
├── tsconfig.json          # TypeScript配置
└── README.md
```

## 核心功能

### 渲染器 (@lowcode/renderer)
- 组件注册系统
- API管理器
- 事件系统
- 主题系统
- Schema解析和渲染

### 设计器 (@lowcode/designer)
- 可视化画布
- 组件面板
- 属性配置面板
- API配置工具
- 拖拽系统

## 业务组件

- TopNavigation - 顶部导航
- SidebarTreeView - 侧边栏树形导航
- TableViewWithSearch - 表格搜索视图
- StatusBar - 状态栏
- AdminLayout - 管理布局

## 开发指南

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建
```bash
npm run build
```

### 测试
```bash
npm run test
```

## 技术栈

- React 18
- TypeScript
- Vite
- Ant Design
- React DnD
- Monaco Editor
