# 第一阶段实施指南：项目初始化和架构搭建

## 🎯 阶段目标

在2周内完成项目的基础架构搭建，为后续开发奠定坚实基础。

## 📅 详细时间安排

### 第1天：项目结构创建

#### 1.1 创建根目录和基础文件

```bash
# 创建项目根目录
mkdir lowcode-platform
cd lowcode-platform

# 初始化根package.json
npm init -y

# 创建基础目录结构
mkdir -p packages/{renderer,designer,shared}
mkdir -p examples docs
```

#### 1.2 配置pnpm工作空间

```yaml
# pnpm-workspace.yaml
packages:
  - 'packages/*'
  - 'examples/*'
```

```json
// package.json
{
  "name": "lowcode-platform",
  "version": "1.0.0",
  "private": true,
  "description": "基于业务组件的低代码开发平台",
  "scripts": {
    "dev:renderer": "pnpm --filter renderer dev",
    "dev:designer": "pnpm --filter designer dev",
    "dev:example": "pnpm --filter example dev",
    "build": "pnpm -r build",
    "test": "pnpm -r test",
    "lint": "pnpm -r lint",
    "type-check": "pnpm -r type-check"
  },
  "devDependencies": {
    "typescript": "^5.0.0",
    "@types/node": "^20.0.0",
    "eslint": "^8.0.0",
    "prettier": "^3.0.0"
  }
}
```

#### 1.3 创建各包的基础结构

```bash
# 渲染器包结构
cd packages/renderer
mkdir -p src/{components,core,types,styles}
touch src/index.ts

# 设计器包结构  
cd ../designer
mkdir -p src/{components,panels,canvas,hooks,utils}
touch src/index.ts

# 共享包结构
cd ../shared
mkdir -p src/{types,utils,constants,schemas}
touch src/index.ts
```

### 第2天：TypeScript配置

#### 2.1 根目录TypeScript配置

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["DOM", "DOM.Iterable", "ES6"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "ESNext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@lowcode/shared/*": ["packages/shared/src/*"],
      "@lowcode/renderer/*": ["packages/renderer/src/*"],
      "@lowcode/designer/*": ["packages/designer/src/*"]
    }
  },
  "include": [
    "packages/*/src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}
```

#### 2.2 各包的package.json配置

```json
// packages/shared/package.json
{
  "name": "@lowcode/shared",
  "version": "1.0.0",
  "main": "dist/index.js",
  "module": "dist/index.esm.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "dev": "vite build --watch",
    "build": "vite build",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "lodash-es": "^4.17.21"
  },
  "devDependencies": {
    "@types/lodash-es": "^4.17.7",
    "vite": "^4.0.0",
    "vite-plugin-dts": "^3.0.0"
  }
}
```

### 第3天：开发工具配置

#### 3.1 ESLint配置

```json
// .eslintrc.json
{
  "env": {
    "browser": true,
    "es2020": true,
    "node": true
  },
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": 11,
    "sourceType": "module"
  },
  "plugins": [
    "react",
    "@typescript-eslint"
  ],
  "rules": {
    "react/react-in-jsx-scope": "off",
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn"
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  }
}
```

#### 3.2 Prettier配置

```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

#### 3.3 Vite配置模板

```typescript
// packages/renderer/vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import dts from 'vite-plugin-dts';
import { resolve } from 'path';

export default defineConfig({
  plugins: [
    react(),
    dts({
      insertTypesEntry: true,
    }),
  ],
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'LowcodeRenderer',
      formats: ['es', 'umd'],
      fileName: (format) => `index.${format}.js`,
    },
    rollupOptions: {
      external: ['react', 'react-dom', 'antd'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
          antd: 'antd',
        },
      },
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@lowcode/shared': resolve(__dirname, '../shared/src'),
    },
  },
});
```

### 第4天：依赖安装

#### 4.1 安装根目录依赖

```bash
# 开发工具依赖
pnpm add -D -w typescript @types/node
pnpm add -D -w eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin
pnpm add -D -w prettier eslint-config-prettier eslint-plugin-prettier
pnpm add -D -w eslint-plugin-react eslint-plugin-react-hooks
pnpm add -D -w @types/react @types/react-dom
```

#### 4.2 安装各包依赖

```bash
# 共享包依赖
cd packages/shared
pnpm add lodash-es
pnpm add -D @types/lodash-es vite vite-plugin-dts

# 渲染器依赖
cd ../renderer  
pnpm add react react-dom antd
pnpm add axios zustand
pnpm add -D vite @vitejs/plugin-react vite-plugin-dts

# 设计器依赖
cd ../designer
pnpm add react react-dom antd
pnpm add react-dnd react-dnd-html5-backend
pnpm add monaco-editor @monaco-editor/react
pnpm add zustand
pnpm add -D vite @vitejs/plugin-react
```

### 第5天：核心类型定义

#### 5.1 Schema类型定义

```typescript
// packages/shared/src/types/schema.ts
export interface PageSchema {
  version: string;
  meta: {
    title: string;
    description: string;
    author?: string;
    createTime?: string;
    updateTime?: string;
  };
  theme: string;
  layout: LayoutConfig;
  components: Record<string, ComponentConfig>;
  apis: Record<string, ApiConfig>;
}

export interface LayoutConfig {
  type: 'admin-layout' | 'simple-layout';
  components: {
    topNavigation?: string;
    sidebar?: string;
    content: string;
    statusBar?: string;
  };
}

export interface ComponentConfig {
  id: string;
  type: string;
  props: Record<string, any>;
  dataBinding?: DataBinding;
  actions?: Record<string, ActionConfig>;
  style?: Record<string, any>;
}

export interface DataBinding {
  source: string;
  autoLoad?: boolean;
  params?: Record<string, any>;
  dependencies?: string[];
}

export interface ActionConfig {
  type: 'api' | 'event' | 'navigation';
  target?: string;
  params?: Record<string, any>;
}

export interface ApiConfig {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  params?: string[];
  headers?: Record<string, string>;
  responseMapping?: string;
  successMessage?: string;
  errorMessage?: string;
}
```

#### 5.2 组件类型定义

```typescript
// packages/shared/src/types/components.ts
export interface TopNavigationProps {
  logo: {
    text: string;
    icon?: string;
    href?: string;
  };
  menus: Array<{
    label: string;
    key: string;
    active: boolean;
    href?: string;
    onClick?: string;
  }>;
  userInfo: {
    avatar?: string;
    name: string;
    showNotification: boolean;
    notificationCount?: number;
  };
}

export interface SidebarTreeViewProps {
  title: string;
  searchable: boolean;
  searchPlaceholder: string;
  tree: TreeNode[];
  defaultSelected?: string;
  onSelect?: string;
}

export interface TreeNode {
  key: string;
  title: string;
  count?: number;
  icon?: string;
  children?: TreeNode[];
}

export interface TableViewWithSearchProps {
  title: string;
  subtitle?: string;
  searchConfig: SearchConfig;
  toolbar: ToolbarConfig;
  table: TableConfig;
  summary?: SummaryConfig;
}

// ... 其他组件类型定义
```

### 第6天：工具函数和常量

#### 6.1 工具函数

```typescript
// packages/shared/src/utils/index.ts
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

export const mergeDeep = (target: any, source: any): any => {
  const output = Object.assign({}, target);
  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target))
          Object.assign(output, { [key]: source[key] });
        else
          output[key] = mergeDeep(target[key], source[key]);
      } else {
        Object.assign(output, { [key]: source[key] });
      }
    });
  }
  return output;
};

const isObject = (item: any): boolean => {
  return item && typeof item === 'object' && !Array.isArray(item);
};

export const formatDate = (date: Date | string): string => {
  const d = new Date(date);
  return d.toLocaleString('zh-CN');
};

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};
```

#### 6.2 常量定义

```typescript
// packages/shared/src/constants/index.ts
export const COMPONENT_TYPES = {
  TOP_NAVIGATION: 'TopNavigation',
  SIDEBAR_TREE_VIEW: 'SidebarTreeView',
  TABLE_VIEW_WITH_SEARCH: 'TableViewWithSearch',
  STATUS_BAR: 'StatusBar',
} as const;

export const LAYOUT_TYPES = {
  ADMIN_LAYOUT: 'admin-layout',
  SIMPLE_LAYOUT: 'simple-layout',
} as const;

export const EVENT_TYPES = {
  MENU_CHANGE: 'menuChange',
  TREE_NODE_SELECT: 'treeNodeSelect',
  TABLE_REFRESH: 'tableRefresh',
  COMPONENT_SELECT: 'componentSelect',
} as const;

export const API_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
} as const;

export const DEFAULT_THEME = 'dataSecurityTheme';

export const DEFAULT_PAGE_SIZE = 20;
```

### 第7天：基础架构验证

#### 7.1 创建示例项目

```bash
# 创建示例项目
mkdir examples/basic-example
cd examples/basic-example

# 初始化示例项目
npm init -y
```

```json
// examples/basic-example/package.json
{
  "name": "basic-example",
  "version": "1.0.0",
  "scripts": {
    "dev": "vite",
    "build": "vite build"
  },
  "dependencies": {
    "@lowcode/renderer": "workspace:*",
    "@lowcode/shared": "workspace:*",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "antd": "^5.0.0"
  },
  "devDependencies": {
    "vite": "^4.0.0",
    "@vitejs/plugin-react": "^4.0.0"
  }
}
```

#### 7.2 验证类型系统

```typescript
// examples/basic-example/src/test-schema.ts
import { PageSchema } from '@lowcode/shared/types';

export const testSchema: PageSchema = {
  version: '1.0.0',
  meta: {
    title: '测试页面',
    description: '用于验证基础架构',
  },
  theme: 'dataSecurityTheme',
  layout: {
    type: 'admin-layout',
    components: {
      content: 'testComponent',
    },
  },
  components: {
    testComponent: {
      id: 'test-1',
      type: 'div',
      props: {
        children: 'Hello Lowcode Platform!',
      },
    },
  },
  apis: {},
};
```

### 第8天：构建系统验证

#### 8.1 配置构建脚本

```bash
# 验证各包构建
pnpm build

# 验证类型检查
pnpm type-check

# 验证代码规范
pnpm lint
```

#### 8.2 设置开发脚本

```json
// package.json 添加更多脚本
{
  "scripts": {
    "dev:all": "concurrently \"pnpm dev:renderer\" \"pnpm dev:designer\" \"pnpm dev:example\"",
    "clean": "pnpm -r exec rm -rf dist node_modules",
    "fresh": "pnpm clean && pnpm install",
    "release": "pnpm build && pnpm test"
  }
}
```

### 第9-10天：文档和规范

#### 9.1 创建开发文档

```markdown
# packages/README.md
# 低代码平台开发指南

## 项目结构
- `packages/shared`: 共享类型和工具
- `packages/renderer`: 渲染器核心
- `packages/designer`: 可视化设计器
- `examples/`: 示例项目

## 开发流程
1. 修改共享类型后，需要重新构建
2. 组件开发遵循统一的接口规范
3. 提交前必须通过类型检查和代码规范检查

## 命名规范
- 组件名使用PascalCase
- 文件名使用kebab-case
- 常量使用UPPER_SNAKE_CASE
```

#### 9.2 Git配置

```bash
# .gitignore
node_modules/
dist/
.DS_Store
*.log
.env.local
.env.development.local
.env.test.local
.env.production.local
```

```json
// .vscode/settings.json
{
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

## ✅ 第一阶段验收标准

完成第一阶段后，应该达到以下标准：

1. **项目结构完整**：monorepo结构清晰，包划分合理
2. **开发环境就绪**：TypeScript、ESLint、Prettier配置正确
3. **依赖安装完成**：所有必要依赖已安装并可正常使用
4. **类型系统完善**：核心类型定义完整，支持类型检查
5. **构建系统可用**：各包可以正常构建和开发
6. **示例项目可运行**：基础示例可以启动和验证

## 🚀 下一步

完成第一阶段后，立即开始第二阶段的渲染器核心开发。
