# 🚀 低代码平台MVP快速启动指南

## 📋 开发前准备

### 环境要求
- Node.js >= 18.0.0
- pnpm >= 8.0.0 (推荐使用pnpm作为包管理器)
- Git

### 安装pnpm
```bash
npm install -g pnpm
```

## 🏗️ 立即开始

### 1. 克隆并初始化项目 (5分钟)

```bash
# 如果还没有创建项目，按照以下步骤
mkdir lowcode-platform
cd lowcode-platform
git init

# 复制第一阶段实施指南中的项目结构
# 创建所有必要的目录和文件
```

### 2. 一键安装依赖 (3分钟)

```bash
# 安装所有依赖
pnpm install

# 如果遇到依赖问题，可以清理后重新安装
pnpm clean && pnpm install
```

### 3. 验证环境 (2分钟)

```bash
# 类型检查
pnpm type-check

# 代码规范检查
pnpm lint

# 构建验证
pnpm build
```

## 🎯 开发流程

### 日常开发命令

```bash
# 启动渲染器开发
pnpm dev:renderer

# 启动设计器开发  
pnpm dev:designer

# 启动示例项目
pnpm dev:example

# 同时启动所有项目
pnpm dev:all
```

### 代码提交流程

```bash
# 提交前检查
pnpm lint          # 代码规范检查
pnpm type-check    # 类型检查
pnpm test          # 运行测试

# 提交代码
git add .
git commit -m "feat: 添加新功能"
```

## 📁 项目结构说明

```
lowcode-platform/
├── packages/
│   ├── shared/           # 🔧 共享代码
│   │   ├── types/        # TypeScript类型定义
│   │   ├── utils/        # 工具函数
│   │   └── constants/    # 常量定义
│   ├── renderer/         # 🎨 渲染器
│   │   ├── components/   # 业务组件
│   │   ├── core/         # 核心功能
│   │   └── styles/       # 样式文件
│   └── designer/         # 🛠️ 设计器
│       ├── components/   # 设计器组件
│       ├── panels/       # 配置面板
│       └── canvas/       # 画布相关
├── examples/             # 📚 示例项目
├── docs/                 # 📖 文档
└── package.json          # 根配置文件
```

## 🔄 开发阶段指南

### 当前阶段：第一阶段 - 项目初始化 ⏳

**目标**：搭建项目基础架构

**任务清单**：
- [ ] 创建项目结构
- [ ] 配置开发环境
- [ ] 安装基础依赖
- [ ] 搭建基础架构

**预计完成时间**：2周

### 下一阶段：第二阶段 - 渲染器核心开发

**目标**：实现渲染器核心功能

**主要任务**：
- 组件注册系统
- API管理器
- 事件系统
- 主题系统
- 核心渲染器

## 🛠️ 开发规范

### 代码规范

1. **命名规范**
   - 组件名：PascalCase (如 `TopNavigation`)
   - 文件名：kebab-case (如 `top-navigation.tsx`)
   - 变量名：camelCase (如 `selectedMenu`)
   - 常量名：UPPER_SNAKE_CASE (如 `COMPONENT_TYPES`)

2. **文件组织**
   - 每个组件一个文件夹
   - index.ts作为导出入口
   - 类型定义放在types文件夹

3. **导入顺序**
   ```typescript
   // 1. React相关
   import React from 'react';
   
   // 2. 第三方库
   import { Button } from 'antd';
   
   // 3. 内部模块
   import { ComponentConfig } from '@lowcode/shared/types';
   
   // 4. 相对路径
   import './styles.css';
   ```

### Git提交规范

使用约定式提交格式：

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

**类型说明**：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构代码
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**：
```
feat(renderer): 添加TopNavigation组件

- 实现Logo展示功能
- 添加菜单导航逻辑
- 支持用户信息展示

Closes #123
```

## 🐛 常见问题解决

### 1. 依赖安装失败

```bash
# 清理缓存
pnpm store prune

# 删除node_modules重新安装
rm -rf node_modules
pnpm install
```

### 2. 类型检查错误

```bash
# 检查TypeScript配置
pnpm type-check

# 重新生成类型文件
pnpm build
```

### 3. 构建失败

```bash
# 检查各包依赖关系
pnpm list

# 单独构建有问题的包
pnpm --filter shared build
```

### 4. 开发服务器启动失败

```bash
# 检查端口占用
lsof -i :3000

# 使用不同端口
PORT=3001 pnpm dev:renderer
```

## 📞 获取帮助

### 文档资源
- [第一阶段实施指南](./第一阶段实施指南.md)
- [MVP编码实现计划](./MVP编码实现计划.md)
- [业务组件设计](./业务组件设计.md)

### 开发工具推荐

1. **VS Code插件**
   - TypeScript Importer
   - ES7+ React/Redux/React-Native snippets
   - Prettier - Code formatter
   - ESLint

2. **Chrome插件**
   - React Developer Tools
   - Redux DevTools

### 调试技巧

1. **组件调试**
   ```typescript
   // 使用React DevTools
   console.log('Component props:', props);
   
   // 使用断点调试
   debugger;
   ```

2. **API调试**
   ```typescript
   // 在API管理器中添加日志
   console.log('API Request:', { url, params });
   console.log('API Response:', response);
   ```

## 🎉 开发里程碑

### 第1周目标
- ✅ 项目结构创建完成
- ✅ 开发环境配置完成
- ✅ 基础依赖安装完成

### 第2周目标
- ✅ 核心类型定义完成
- ✅ 工具函数实现完成
- ✅ 构建系统验证通过

### 第4周目标
- 🎯 渲染器核心功能完成
- 🎯 组件注册系统可用
- 🎯 API管理器可用

### 第6周目标
- 🎯 4个核心业务组件完成
- 🎯 可以渲染完整页面
- 🎯 基础交互功能可用

### 第8周目标
- 🎯 设计器基础功能完成
- 🎯 可以生成JSON Schema
- 🎯 预览功能可用

### 第10周目标
- 🎯 MVP功能完整
- 🎯 测试覆盖率达标
- 🎯 文档完善
- 🎯 可以正式使用

## 🚀 立即行动

1. **现在就开始**：按照第一阶段实施指南创建项目结构
2. **设置提醒**：每日检查任务进度
3. **寻求反馈**：定期与产品经理沟通需求
4. **持续改进**：根据开发过程中的问题调整计划

---

**记住**：MVP的目标是快速验证核心功能，不要追求完美，先让基础功能跑起来！
