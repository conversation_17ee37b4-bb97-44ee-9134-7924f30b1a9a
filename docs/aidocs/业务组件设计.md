# 业务组件设计方案

## 1. 核心业务组件

### 1.1 TopNavigation - 顶部导航组件
**功能**：顶部主导航菜单，包含 Logo、主菜单、用户信息
```typescript
interface TopNavigationProps {
  logo: {
    text: string;
    icon?: string;
  };
  menus: Array<{
    label: string;
    key: string;
    active: boolean;
    onClick?: string; // 事件处理
  }>;
  userInfo: {
    avatar?: string;
    name: string;
    showNotification: boolean;
    notificationCount?: number;
  };
}
```

### 1.2 SidebarTreeView - 侧边栏树形导航组件
**功能**：左侧树形导航，支持搜索、计数显示、层级展开
```typescript
interface SidebarTreeViewProps {
  title: string;
  searchable: boolean;
  searchPlaceholder: string;
  tree: TreeNode[];
  defaultSelected: string;
  onSelect?: string; // 选择事件
}

interface TreeNode {
  key: string;
  title: string;
  count?: number;
  icon?: string;
  children?: TreeNode[];
}
```

### 1.3 TableViewWithSearch - 表格视图搜索组件（核心）
**功能**：集成搜索、工具栏、表格、分页的完整数据展示组件
```typescript
interface TableViewWithSearchProps {
  title: string;
  subtitle?: string;
  searchConfig: SearchConfig;
  toolbar: ToolbarConfig;
  table: TableConfig;
  summary?: SummaryConfig;
}

interface SearchConfig {
  fields: SearchField[];
  layout: 'inline' | 'grid';
  showReset: boolean;
  showSearch: boolean;
}

interface ToolbarConfig {
  left: ToolbarItem[];
  right: ToolbarItem[];
}

interface TableConfig {
  columns: ColumnConfig[];
  pagination: PaginationConfig;
  selection?: SelectionConfig;
}
```

### 1.4 StatusBar - 状态栏组件
**功能**：底部状态栏，显示版权、版本等信息
```typescript
interface StatusBarProps {
  left: {
    copyright: string;
  };
  right: {
    version: string;
  };
}
```

## 2. 布局系统

### 2.1 AdminLayout - 管理后台布局
```typescript
interface AdminLayoutConfig {
  type: "admin-layout";
  components: {
    topNavigation: string; // 组件ID
    sidebar: string;       // 组件ID
    content: string;       // 组件ID
    statusBar: string;     // 组件ID
  };
}
```

### 2.2 布局渲染逻辑
```typescript
const AdminLayout: React.FC<{
  topNavigation: React.ComponentType;
  sidebar: React.ComponentType;
  content: React.ComponentType;
  statusBar: React.ComponentType;
}> = ({ topNavigation: TopNav, sidebar: Sidebar, content: Content, statusBar: StatusBar }) => {
  return (
    <div className="admin-layout">
      <header className="admin-header">
        <TopNav />
      </header>
      
      <div className="admin-body">
        <aside className="admin-sidebar">
          <Sidebar />
        </aside>
        
        <main className="admin-content">
          <Content />
        </main>
      </div>
      
      <footer className="admin-footer">
        <StatusBar />
      </footer>
    </div>
  );
};
```

## 3. 组件配置详解

### 3.1 TableViewWithSearch 详细配置

#### 搜索配置
```typescript
interface SearchField {
  name: string;
  label: string;
  type: 'input' | 'select' | 'date' | 'dateRange';
  placeholder?: string;
  options?: Option[] | string; // 静态选项或API名称
  defaultValue?: any;
}
```

#### 工具栏配置
```typescript
interface ToolbarItem {
  type: 'button' | 'tabs' | 'dropdown';
  label?: string;
  icon?: string;
  style?: 'primary' | 'default';
  items?: Array<{
    label: string;
    key: string;
    active?: boolean;
  }>;
  onClick?: string; // 事件处理
}
```

#### 表格列配置
```typescript
interface ColumnConfig {
  title: string;
  dataIndex: string;
  type: 'text' | 'number' | 'tag' | 'datetime' | 'actions';
  width?: number;
  sortable?: boolean;
  
  // tag 类型特有
  mapping?: Record<string, {
    text: string;
    color: string;
  }>;
  
  // actions 类型特有
  actions?: ActionConfig[];
}

interface ActionConfig {
  label: string;
  type: 'button' | 'drawer' | 'modal' | 'dropdown';
  target?: string; // 目标组件ID
  icon?: string;
  confirm?: string; // 确认文本
  items?: ActionConfig[]; // dropdown 子项
}
```

## 4. 事件系统

### 4.1 组件间通信
```typescript
interface EventConfig {
  // 导航选择事件
  onNavSelect: {
    target: string; // 目标组件ID
    action: 'updateData' | 'changeView';
    params?: Record<string, any>;
  };
  
  // 搜索事件
  onSearch: {
    target: string;
    action: 'refreshTable';
    params: 'searchValues'; // 搜索表单值
  };
  
  // 表格操作事件
  onTableAction: {
    type: 'edit' | 'delete' | 'view';
    target?: string; // 目标组件（如抽屉、弹窗）
    api?: string;    // API调用
  };
}
```

### 4.2 数据绑定
```typescript
interface DataBinding {
  source: string; // API名称
  autoLoad: boolean;
  params?: Record<string, any>; // 静态参数
  dependencies?: string[]; // 依赖的其他组件
}
```

## 5. 主题系统

### 5.1 数据安全主题
```typescript
interface DataSecurityTheme {
  // 颜色系统
  colors: {
    primary: '#1890ff';
    success: '#52c41a';
    warning: '#faad14';
    error: '#f5222d';
    
    // 风险等级颜色
    riskHigh: '#ff4d4f';
    riskMedium: '#fa8c16';
    riskLow: '#1890ff';
  };
  
  // 组件样式
  components: {
    topNavigation: {
      backgroundColor: '#001529';
      textColor: '#ffffff';
    };
    sidebar: {
      backgroundColor: '#f0f2f5';
      borderColor: '#d9d9d9';
    };
    table: {
      headerBackground: '#fafafa';
      borderColor: '#f0f0f0';
    };
  };
}
```

## 6. 组件扩展机制

### 6.1 自定义业务组件
```typescript
interface CustomComponent {
  type: string; // 组件类型名
  component: React.ComponentType; // React组件
  configSchema: JSONSchema; // 配置项Schema
  defaultProps: any; // 默认属性
}

// 注册自定义组件
componentRegistry.register('CustomTableView', {
  component: CustomTableViewComponent,
  configSchema: customTableConfigSchema,
  defaultProps: {
    showExportButton: true,
    enableBatchOperation: true
  }
});
```

### 6.2 组件组合
```typescript
// 支持组件嵌套和组合
interface CompositeComponent {
  type: 'composite';
  layout: 'horizontal' | 'vertical' | 'grid';
  components: ComponentConfig[];
}
```
