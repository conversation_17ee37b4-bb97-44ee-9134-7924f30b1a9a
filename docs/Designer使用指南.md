# 设计器使用指南

## 快速开始

- 导出：从 @lowcode/designer 引入 DesignerProvider、ComponentPanel、Canvas、PropertyPanel、ApiPanel、Toolbar。
- 组合：使用 Provider 包裹，左侧 ComponentPanel，中间 Canvas，右侧 PropertyPanel/ApiPanel，上方 Toolbar。

## 组件一览
- ComponentPanel：组件库浏览与拖拽/点击添加
- Canvas：设计/预览渲染画布，支持选中/悬停高亮
- PropertyPanel：按元数据动态渲染表单，编辑选中组件属性
- ApiPanel：页面级 API 增删改查与占位测试
- Toolbar：设计/预览切换与导出 Schema

## 示例
```tsx
import { DesignerProvider, ComponentPanel, Canvas, PropertyPanel, ApiPanel, Toolbar } from '@lowcode/designer';

export default () => (
  <DesignerProvider>
    <Toolbar />
    <div style={{ display: 'flex', height: 'calc(100vh - 48px)' }}>
      <ComponentPanel />
      <div style={{ flex: 1 }}><Canvas /></div>
      <PropertyPanel />
    </div>
    <ApiPanel />
  </DesignerProvider>
);
```

## 常见问题
- 预览无效：确认 Toolbar 已切换到“预览”，Canvas 会去除设计时交互。
- 属性无效：组件元数据中无对应 prop 时，请在“通用”区域编辑 className/style。
- API 测试：当前为控制台打印占位，后续可接入 axios/fetch 进行真请求。

