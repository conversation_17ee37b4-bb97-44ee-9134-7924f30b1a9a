{"name": "lowcode-simple-app", "version": "1.0.0", "description": "Simple app example using lowcode platform", "main": "src/index.tsx", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@lowcode/renderer": "file:../../packages/renderer", "@lowcode/designer": "file:../../packages/designer"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "typescript": "^5.0.0", "vite": "^4.0.0"}}