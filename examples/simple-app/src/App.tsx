import React, { useEffect, useMemo, useState } from 'react';
import { createRenderer } from '@lowcode/renderer';
import type { PageSchema } from '@lowcode/renderer';
import { basicComponentMetas, businessComponentMetas, layoutComponentMetas, createDefaultComponentSchema } from '@lowcode/renderer';
import { ComponentLibrarySidebar } from '@lowcode/renderer';
import { DesignerProvider, Canvas, PropertyDrawer, Toolbar, useDesigner } from '@lowcode/designer';

// API 管理页面 Schema（接近截图效果）
const apiPageSchema: PageSchema = {
  id: 'api_management',
  title: '全部API',
  components: [
    { id: 'top_nav', type: 'TopNavigation', props: {
        logo: { text: '金知科技', href: '/' },
        menu: [
          { key: 'overview', label: '概览', href: '/overview' },
          { key: 'apis', label: 'APIs', href: '/apis' },
          { key: 'data', label: '数据', href: '/data' },
          { key: 'insight', label: '洞察', href: '/insight' },
          { key: 'risk', label: '风险', href: '/risk' },
          { key: 'audit', label: '审计', href: '/audit' },
          { key: 'config', label: '配置', href: '/config' }
        ],
        user: { name: 'book', menu: [{ key: 'logout', label: '退出' }] }
    }},
    { id: 'main', type: 'Container', props: { style: { display: 'flex', minHeight: 'calc(100vh - 96px)' } },
      children: [
        { id: 'left_sidebar', type: 'Container', props: { style: { width: '240px' } },
          children: [
            { id: 'sidebar', type: 'SidebarTreeView', props: {
                width: 240, height: '100%', searchable: true, searchPlaceholder: '搜索视图/分类',
                data: [
                  { key: 'all', title: '全部API（212）', href: '/apis/all' },
                  { key: 'access', title: '接入类API', children: [ { key: 'login', title: '登录类API（36）' }, { key: 'url', title: 'URL查询类API（41）' } ] },
                  { key: 'quality', title: '单API调用度及覆盖率', children: [ { key: 'high', title: '高频（58）' }, { key: 'mid', title: '中频（8）' }, { key: 'low', title: '低频（19）' } ] },
                  { key: 'risk', title: '报警及风险项API' },
                  { key: 'app', title: '应用视图' }
                ]
            }}
          ]
        },
        { id: 'content', type: 'Container', props: { style: { flex: 1, display: 'flex', flexDirection: 'column' } },
          children: [
            { id: 'content_header', type: 'Container', props: { style: { display: 'flex', alignItems: 'center', justifyContent: 'space-between' } },
              children: [
                { id: 'page_title', type: 'Text', props: { children: '全部API' } },
                { id: 'header_actions', type: 'Container', props: { },
                  children: [
                    { id: 'search_input', type: 'Input', props: { placeholder: '请输入URL进行筛选' } },
                    { id: 'btn_batch', type: 'Button', props: { children: '批量操作' } },
                    { id: 'btn_saveview', type: 'Button', props: { children: '保存视图' } },
                    { id: 'btn_more', type: 'Button', props: { children: '更多设置' } }
                  ]
                }
              ]
            },
            { id: 'api_table', type: 'TableViewWithSearch', props: {
                searchFields: [
                  { key: 'app', label: '应用', type: 'select', placeholder: '请选择', options: [ { label: '全部', value: '' }, { label: '应用A', value: 'A' }, { label: '应用B', value: 'B' } ] },
                  { key: 'appName', label: '应用名称', type: 'input', placeholder: '请输入' },
                  { key: 'mode', label: '访问模式', type: 'select', options: [ { label: 'GET', value: 'GET' }, { label: 'POST', value: 'POST' } ] },
                  { key: 'terminal', label: '终端类型', type: 'select', options: [ { label: 'Web', value: 'web' }, { label: '移动端', value: 'mobile' } ] },
                  { key: 'respType', label: '请求类型', type: 'select', options: [ { label: 'JSON', value: 'json' }, { label: 'HTML', value: 'html' } ] },
                  { key: 'callType', label: '响应标签', type: 'select', options: [ { label: '内部', value: 'internal' }, { label: '外部', value: 'external' } ] },
                  { key: 'firstSeen', label: '首次发现时间', type: 'date' },
                  { key: 'sensitivity', label: 'API感度等级', type: 'select', options: [ { label: '高敏感', value: '高敏感' }, { label: '中敏感', value: '中敏感' }, { label: '低敏感', value: '低敏感' }, { label: '非敏感', value: '非敏感' } ] }
                ],
                toolbarActions: [ { key: 'export', label: '导出', type: 'default', icon: '📤' }, { key: 'refresh', label: '刷新', type: 'default', icon: '🔄' } ],
                columns: [
                  { key: 'path', title: '路径', dataIndex: 'path', width: 320 },
                  { key: 'sensitivity', title: 'API感度等级', dataIndex: 'sensitivity' },
                  { key: 'totalVisits', title: '累计访问次数', dataIndex: 'totalVisits', sortable: true },
                  { key: 'coverage', title: '目检覆盖率', dataIndex: 'coverage' },
                  { key: 'ip', title: 'IP', dataIndex: 'ip' },
                  { key: 'firstSeen', title: '首次发现时间', dataIndex: 'firstSeen', sortable: true }
                ],
                rowActions: [ { key: 'view', label: '查看', type: 'primary', icon: '👁️' }, { key: 'more', label: '更多', type: 'default', icon: '⋯' } ],
                dataSource: [
                  { id: 1, path: '/k8s-api/${param1}/${param2}', sensitivity: '高敏感', totalVisits: '6.4千', coverage: '—', ip: '*************', firstSeen: '2025-08-14 19:44:25' },
                  { id: 2, path: '/login', sensitivity: '高敏感', totalVisits: '3.6千', coverage: '—', ip: '***********', firstSeen: '2025-08-14 19:38:21' },
                  { id: 3, path: '/abnormal', sensitivity: '高敏感', totalVisits: '2.5千', coverage: '—', ip: '***********1', firstSeen: '2025-08-14 19:02:19' },
                  { id: 4, path: '/login', sensitivity: '高敏感', totalVisits: '502', coverage: '—', ip: '************', firstSeen: '2025-08-14 19:00:18' },
                  { id: 5, path: '/login', sensitivity: '高敏感', totalVisits: '327', coverage: '—', ip: '************', firstSeen: '2025-08-14 19:00:17' }
                ],
                pagination: { current: 1, pageSize: 25, total: 589, showSizeChanger: true, showQuickJumper: true }
            }}
          ]
        }
      ]
    },
    { id: 'status_bar', type: 'StatusBar', props: { items: [ { key: 'copyright', label: 'Copyright', value: '© 2017-2025 All rights reserved.' }, { key: 'version', label: '版本', value: '3.3.0.20250731' } ] } }
  ],
  apis: [],
  theme: undefined as any
};


// 设计器内部桥接：把设计器上下文中的 schema 同步回父组件
const SchemaSyncBridge: React.FC<{ onChange: (schema: PageSchema) => void }> = ({ onChange }) => {
  const { schema } = useDesigner();
  useEffect(() => {
    onChange(schema);
  }, [schema, onChange]);
  return null;
};

const App: React.FC = () => {
  const [renderer] = useState(() => createRenderer());
  const [currentSchema, setCurrentSchema] = useState<PageSchema>(apiPageSchema);
  const [designerMode, setDesignerMode] = useState<boolean>(false);

  // 顶部切换按钮样式
  const TopBar = useMemo(() => (
    <div style={{ height: 56, display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '0 16px', borderBottom: '1px solid #e5e7eb', background: '#ffffff' }}>
      <div style={{ fontWeight: 600 }}>低代码平台示例</div>
      <div style={{ display: 'flex', gap: 8 }}>
        <button className="lc-btn lc-btn-default" onClick={() => setDesignerMode(false)} disabled={!designerMode}>仅渲染</button>
        <button className="lc-btn lc-btn-primary" onClick={() => setDesignerMode(true)} disabled={designerMode}>进入设计器</button>
      </div>
    </div>
  ), [designerMode]);


// 左侧组件面板：可折叠容器，贴合设计稿侧边栏交互
const CollapsibleComponentPanel: React.FC = () => {
  const { addComponent } = useDesigner();
  const allMetas = [...basicComponentMetas, ...businessComponentMetas, ...layoutComponentMetas];

  return (
    <div style={{ display: 'block' }}>
      <ComponentLibrarySidebar
        defaultTab="basics"
        layoutItems={[{ key: 'Container', label: '容器', icon: 'view_quilt' }]}
        basicItems={[
          { key: 'Text', label: '文本', icon: 'text_fields' },
          { key: 'Button', label: '按钮', icon: 'smart_button' },
          { key: 'Input', label: '输入框', icon: 'input' },
          { key: 'Image', label: '图片', icon: 'image' },
        ]}
        businessItems={[
          { key: 'TableViewWithSearch', label: '表格', icon: 'table_chart' },
          { key: 'TopNavigation', label: '导航', icon: 'view_sidebar' },
          { key: 'SidebarTreeView', label: '侧栏', icon: 'account_tree' },
          { key: 'StatusBar', label: '状态栏', icon: 'view_day' },
        ]}
        onItemClick={(_, item) => {
          const meta = allMetas.find(m => m.type === item.key);
          const schema = createDefaultComponentSchema(item.key, meta);
          addComponent(schema);
        }}
      />
    </div>
  );
};

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {TopBar}

      {/* 设计器模式 */}
      {designerMode ? (
        <DesignerProvider initialSchema={currentSchema}>
          <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            <Toolbar />
            <div style={{ flex: 1, display: 'flex', position: 'relative', overflow: 'hidden' }}>
              <CollapsibleComponentPanel />
              <div style={{ flex: 1, minWidth: 0 }}>
                <Canvas />
              </div>
              {/* 右侧属性编辑以抽屉覆盖在预览区上方 */}
              <PropertyDrawer />
            </div>
          </div>
          {/* 同步设计器中的 schema 到父组件，从而影响渲染器视图 */}
          <SchemaSyncBridge onChange={setCurrentSchema} />
        </DesignerProvider>
      ) : (
        // 仅渲染视图，展示 currentSchema
        <div style={{ flex: 1 }}>
          {renderer.renderPage(currentSchema)}
        </div>
      )}
    </div>
  );
};

export default App;
