<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>低代码平台设计器演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }
        
        .designer-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        /* 顶部工具栏 */
        .toolbar {
            height: 48px;
            background: #ffffff;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            flex-shrink: 0;
            z-index: 20;
        }
        
        .toolbar h1 {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
            margin: 0;
        }
        
        .toolbar .preview-btn {
            background: #2563eb;
            color: #ffffff;
            font-weight: 600;
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .toolbar .preview-btn:hover {
            background: #1d4ed8;
        }
        
        /* 主体区域 */
        .main-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        /* 左侧组件面板 */
        .component-panel {
            width: 264px;
            background: #ffffff;
            border-right: 1px solid #e8e8e8;
            display: flex;
            overflow: hidden;
            position: relative;
            flex-shrink: 0;
        }
        
        .component-nav {
            width: 80px;
            border-right: 1px solid #e8e8e8;
            background: #ffffff;
            padding: 8px;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            border: none;
            border-left: 4px solid transparent;
            border-radius: 6px;
            background: transparent;
            color: #666666;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            min-height: 60px;
            justify-content: center;
            margin-bottom: 4px;
            transition: all 0.2s ease;
        }
        
        .nav-item.active {
            border-left-color: #1890ff;
            background: #e6f7ff;
            color: #1890ff;
            font-weight: 600;
        }
        
        .nav-item:hover:not(.active) {
            background: #f5f5f5;
            color: #333333;
        }
        
        .nav-item .icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .component-list {
            flex: 1;
            overflow: auto;
            padding: 12px;
        }
        
        .component-group h4 {
            font-size: 12px;
            font-weight: 600;
            color: #333333;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }
        
        .component-item {
            padding: 8px;
            background: #ffffff;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            cursor: move;
            text-align: center;
            transition: all 0.2s ease;
            user-select: none;
            margin-bottom: 8px;
        }
        
        .component-item:hover {
            border-color: #1890ff;
            background: #f0f9ff;
        }
        
        .component-item .icon {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .component-item .name {
            font-size: 12px;
            font-weight: 500;
            color: #333333;
        }
        
        /* 中央画布区域 */
        .canvas-area {
            flex: 1;
            background: #f0f2f5;
            padding: 24px;
            overflow: auto;
            position: relative;
        }
        
        .page-container {
            background: #ffffff;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            min-height: 600px;
            position: relative;
        }
        
        /* 示例内容样式 */
        .demo-nav {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 0;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .demo-nav-items {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .demo-nav-item {
            padding: 6px 12px;
            font-size: 14px;
            font-weight: 500;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .demo-nav-item.brand {
            color: #ffffff;
            background: #2563eb;
        }
        
        .demo-nav-item.active {
            color: #2563eb;
            background: #eff6ff;
        }
        
        .demo-nav-item:not(.brand):not(.active) {
            color: #6b7280;
            background: transparent;
        }
        
        .demo-nav-item:not(.brand):not(.active):hover {
            background: #f3f4f6;
            color: #374151;
        }
        
        .demo-search-form {
            padding: 24px;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 24px;
        }
        
        .demo-search-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .demo-form-field label {
            display: block;
            margin-bottom: 4px;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }
        
        .demo-form-field input,
        .demo-form-field select {
            width: 100%;
            height: 40px;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s ease;
        }
        
        .demo-form-field input:focus,
        .demo-form-field select:focus {
            border-color: #2563eb;
        }
        
        .demo-form-field select {
            color: #6b7280;
        }
        
        .demo-form-buttons {
            display: flex;
            gap: 8px;
        }
        
        .demo-btn {
            height: 40px;
            padding: 0 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .demo-btn.primary {
            background: #2563eb;
            color: #ffffff;
            border: none;
        }
        
        .demo-btn.primary:hover {
            background: #1d4ed8;
        }
        
        .demo-btn.secondary {
            background: #ffffff;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .demo-btn.secondary:hover {
            background: #f3f4f6;
        }
        
        .demo-toolbar {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }
        
        .demo-toolbar-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            background: transparent;
            color: #2563eb;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        
        .demo-toolbar-btn:hover {
            color: #1d4ed8;
        }
        
        .demo-table {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            background: #ffffff;
        }
        
        .demo-table table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .demo-table th {
            padding: 12px 24px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            font-size: 12px;
            font-weight: 700;
            color: #374151;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            background: #f9fafb;
        }
        
        .demo-table td {
            padding: 16px 24px;
            border-bottom: 1px solid #f3f4f6;
            font-size: 14px;
        }
        
        .demo-table tr:hover {
            background: #f9fafb;
        }
        
        .demo-table .path-cell {
            color: #111827;
            font-weight: 500;
        }
        
        .demo-table .other-cell {
            color: #6b7280;
        }
        
        .sensitive-tag {
            background: #fef2f2;
            color: #dc2626;
            font-size: 12px;
            font-weight: 500;
            padding: 2px 10px;
            border-radius: 9999px;
            border: 1px solid #fecaca;
        }
        
        .action-btn {
            color: #2563eb;
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 16px;
            padding: 4px;
            transition: color 0.2s ease;
        }
        
        .action-btn:hover {
            color: #1d4ed8;
        }
    </style>
</head>
<body>
    <div class="designer-container">
        <!-- 顶部工具栏 -->
        <header class="toolbar">
            <h1>低代码平台</h1>
            <button class="preview-btn">预览</button>
        </header>
        
        <!-- 主体区域 -->
        <div class="main-content">
            <!-- 左侧组件面板 -->
            <aside class="component-panel">
                <!-- 左侧导航 -->
                <div class="component-nav">
                    <button class="nav-item">
                        <span class="icon">⚏</span>
                        <span>布局</span>
                    </button>
                    <button class="nav-item active">
                        <span class="icon">🧩</span>
                        <span>基础</span>
                    </button>
                    <button class="nav-item">
                        <span class="icon">💼</span>
                        <span>业务</span>
                    </button>
                </div>
                
                <!-- 组件列表 -->
                <div class="component-list">
                    <div class="component-group">
                        <h4>基础组件 <span>▼</span></h4>
                        <div class="component-item">
                            <div class="icon">📝</div>
                            <div class="name">文本</div>
                        </div>
                        <div class="component-item">
                            <div class="icon">🔘</div>
                            <div class="name">按钮</div>
                        </div>
                        <div class="component-item">
                            <div class="icon">📝</div>
                            <div class="name">输入框</div>
                        </div>
                        <div class="component-item">
                            <div class="icon">🖼️</div>
                            <div class="name">图片</div>
                        </div>
                    </div>
                </div>
            </aside>
            
            <!-- 中央画布区域 -->
            <main class="canvas-area">
                <div class="page-container">
                    <!-- 示例导航栏 -->
                    <div class="demo-nav">
                        <div class="demo-nav-items">
                            <button class="demo-nav-item brand">全知科技</button>
                            <button class="demo-nav-item active">概览</button>
                            <button class="demo-nav-item">APIs</button>
                            <button class="demo-nav-item">数据</button>
                            <button class="demo-nav-item">洞察</button>
                            <button class="demo-nav-item">风险</button>
                            <button class="demo-nav-item">审计</button>
                            <button class="demo-nav-item">配置</button>
                        </div>
                    </div>
                    
                    <!-- 示例搜索表单 -->
                    <div class="demo-search-form">
                        <div class="demo-search-grid">
                            <div class="demo-form-field">
                                <label>应用</label>
                                <select>
                                    <option>请选择</option>
                                </select>
                            </div>
                            <div class="demo-form-field">
                                <label>应用名称</label>
                                <input type="text" placeholder="请输入">
                            </div>
                            <div class="demo-form-field">
                                <label>访问模式</label>
                                <select>
                                    <option>请选择</option>
                                </select>
                            </div>
                            <div class="demo-form-field">
                                <label>终端类型</label>
                                <select>
                                    <option>请选择</option>
                                </select>
                            </div>
                            <div class="demo-form-field">
                                <label>请求类型</label>
                                <select>
                                    <option>请选择</option>
                                </select>
                            </div>
                            <div class="demo-form-field">
                                <label>响应告警</label>
                                <select>
                                    <option>请选择</option>
                                </select>
                            </div>
                            <div class="demo-form-field">
                                <label>首次发现时间</label>
                                <input type="text" placeholder="年 / 月 / 日">
                            </div>
                            <div class="demo-form-field">
                                <label>API敏感等级</label>
                                <select>
                                    <option>请选择</option>
                                </select>
                            </div>
                        </div>
                        <div class="demo-form-buttons">
                            <button class="demo-btn primary">搜索</button>
                            <button class="demo-btn secondary">重置</button>
                        </div>
                    </div>
                    
                    <!-- 示例工具栏 -->
                    <div class="demo-toolbar">
                        <button class="demo-toolbar-btn">
                            <span>📥</span>导出
                        </button>
                        <button class="demo-toolbar-btn">
                            <span>🔄</span>刷新
                        </button>
                    </div>
                    
                    <!-- 示例表格 -->
                    <div class="demo-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>路径</th>
                                    <th>API敏感等级</th>
                                    <th>累计访问次数</th>
                                    <th>目检覆盖率</th>
                                    <th>IP</th>
                                    <th>首次发现时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="path-cell">/k8s-api/${param1}/${param2}</td>
                                    <td><span class="sensitive-tag">高敏感</span></td>
                                    <td class="other-cell">6.4k</td>
                                    <td class="other-cell">—</td>
                                    <td class="other-cell">*************</td>
                                    <td class="other-cell">2025-08-14 19:44:25</td>
                                    <td><button class="action-btn">⋯</button></td>
                                </tr>
                                <tr>
                                    <td class="path-cell">/login</td>
                                    <td><span class="sensitive-tag">高敏感</span></td>
                                    <td class="other-cell">3.6k</td>
                                    <td class="other-cell">—</td>
                                    <td class="other-cell">192.168.0.1</td>
                                    <td class="other-cell">2025-08-14 19:38:21</td>
                                    <td><button class="action-btn">⋯</button></td>
                                </tr>
                                <tr>
                                    <td class="path-cell">/abnormal</td>
                                    <td><span class="sensitive-tag">高敏感</span></td>
                                    <td class="other-cell">2.5k</td>
                                    <td class="other-cell">—</td>
                                    <td class="other-cell">192.168.0.11</td>
                                    <td class="other-cell">2025-08-14 19:02:19</td>
                                    <td><button class="action-btn">⋯</button></td>
                                </tr>
                                <tr>
                                    <td class="path-cell">/login</td>
                                    <td><span class="sensitive-tag">高敏感</span></td>
                                    <td class="other-cell">502</td>
                                    <td class="other-cell">—</td>
                                    <td class="other-cell">192.168.0.21</td>
                                    <td class="other-cell">2025-08-14 19:00:18</td>
                                    <td><button class="action-btn">⋯</button></td>
                                </tr>
                                <tr>
                                    <td class="path-cell">/login</td>
                                    <td><span class="sensitive-tag">高敏感</span></td>
                                    <td class="other-cell">327</td>
                                    <td class="other-cell">—</td>
                                    <td class="other-cell">192.168.0.15</td>
                                    <td class="other-cell">2025-08-14 19:00:17</td>
                                    <td><button class="action-btn">⋯</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
